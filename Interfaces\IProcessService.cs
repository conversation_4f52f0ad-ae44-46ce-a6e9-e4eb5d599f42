using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Birleştirilmiş işlem yönetimi servisinin arayüzü
/// Proje işlemlerinin CRUD operasyonlarını, durum yönetimini ve arka plan görevlerini tanımlar
/// </summary>
public interface IProcessService
{
    // CRUD Operations
    /// <summary>
    /// Belirtilen projeye ait tüm işlemleri listeler
    /// </summary>
    /// <param name="projectId"><PERSON>je kimliğ<PERSON></param>
    /// <returns>İşlem listesi</returns>
    Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(long projectId);

    /// <summary>
    /// Belirtilen ID'ye sahip işlemi getirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İşlem bilgileri veya null</returns>
    Task<Process?> GetProcessByIdAsync(Guid id);

    /// <summary>
    /// Yeni işlem oluşturur ve başlatır
    /// </summary>
    /// <param name="processDto">İşlem bilgileri</param>
    /// <returns>Oluşturulan ve başlatılan işlem</returns>
    Task<Process> CreateAndStartProcessAsync(ProcessCreateDto processDto);

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <param name="processUpdateDto">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem veya null</returns>
    Task<Process?> UpdateProcessAsync(Guid id, ProcessUpdateDto processUpdateDto);

    /// <summary>
    /// İşlemi siler
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    Task<bool> DeleteProcessAsync(Guid id);

    /// <summary>
    /// İşlemi duraklatır
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Duraklama işleminin başarılı olup olmadığı</returns>
    Task<bool> PauseProcessAsync(Guid id);

    /// <summary>
    /// İşlemi devam ettirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Devam ettirme işleminin başarılı olup olmadığı</returns>
    Task<bool> ResumeProcessAsync(Guid id);

    /// <summary>
    /// İşlemi iptal eder
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İptal işleminin başarılı olup olmadığı</returns>
    Task<bool> CancelProcessAsync(Guid id);

    /// <summary>
    /// İşlemin son iletişim zamanını günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Güncelleme işleminin başarılı olup olmadığı</returns>
    Task<bool> UpdateLastPingAsync(Guid id);

}
