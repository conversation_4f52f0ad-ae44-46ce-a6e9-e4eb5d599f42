using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Google Sheets API işlemleri için servis arayüzü
/// </summary>
public interface IGoogleSheetsService
{
    /// <summary>
    /// Service account JSON ile Google Sheets servisini başlatır
    /// </summary>
    /// <param name="serviceAccountJson">Service account JSON içeriği</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> InitializeServiceAsync(string serviceAccountJson);

    /// <summary>
    /// Kullanıcının erişebildiği tüm spreadsheet'leri listeler
    /// </summary>
    /// <returns>Spreadsheet listesi</returns>
    Task<GoogleSheetsApiResponse<List<SpreadsheetInfo>>> GetSpreadsheetsAsync();

    /// <summary>
    /// Belirtilen spreadsheet'in tüm sheet'lerini listeler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Sheet listesi</returns>
    Task<GoogleSheetsApiResponse<List<SheetInfo>>> GetSheetsAsync(string spreadsheetId);

    /// <summary>
    /// Belirtilen aralıktaki verileri getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Veri aralığı (örn: Sheet1!A1:C10)</param>
    /// <returns>Aralık verisi</returns>
    Task<GoogleSheetsApiResponse<RangeData>> GetRangeDataAsync(string spreadsheetId, string range);

    /// <summary>
    /// Belirtilen sheet'in tüm verilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>Sheet verisi</returns>
    Task<GoogleSheetsApiResponse<List<List<string>>>> GetSheetDataAsync(string spreadsheetId, string sheetName);

    /// <summary>
    /// Belirtilen sheet'e yeni satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowData">Eklenecek satır verisi</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> AddRowAsync(string spreadsheetId, string sheetName, SheetRowData rowData);

    /// <summary>
    /// Belirtilen sheet'e birden fazla satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowsData">Eklenecek satırlar</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> AddRowsAsync(string spreadsheetId, string sheetName, List<SheetRowData> rowsData);

    /// <summary>
    /// Belirtilen satırı siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="rowIndex">Silinecek satır indeksi (0-based)</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> DeleteRowAsync(string spreadsheetId, int sheetId, int rowIndex);

    /// <summary>
    /// Belirtilen satır aralığını siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="startRowIndex">Başlangıç satır indeksi (0-based)</param>
    /// <param name="endRowIndex">Bitiş satır indeksi (0-based, dahil değil)</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> DeleteRowsAsync(string spreadsheetId, int sheetId, int startRowIndex, int endRowIndex);

    /// <summary>
    /// Belirtilen hücreyi günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Hücre aralığı (örn: Sheet1!A1)</param>
    /// <param name="value">Yeni değer</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> UpdateCellAsync(string spreadsheetId, string range, string value);

    /// <summary>
    /// Belirtilen aralığı günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Aralık (örn: Sheet1!A1:C3)</param>
    /// <param name="values">Yeni değerler</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> UpdateRangeAsync(string spreadsheetId, string range, List<List<string>> values);

    /// <summary>
    /// Toplu güncelleme yapar
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="batchRequest">Toplu güncelleme isteği</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> BatchUpdateAsync(string spreadsheetId, BatchUpdateRequest batchRequest);

    /// <summary>
    /// Yeni bir sheet oluşturur
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="createRequest">Sheet oluşturma isteği</param>
    /// <returns>Oluşturulan sheet bilgisi</returns>
    Task<GoogleSheetsApiResponse<SheetInfo>> CreateSheetAsync(string spreadsheetId, CreateSheetRequest createRequest);

    /// <summary>
    /// Belirtilen sheet'i siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Silinecek sheet ID'si</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> DeleteSheetAsync(string spreadsheetId, int sheetId);

    /// <summary>
    /// Yeni bir spreadsheet oluşturur
    /// </summary>
    /// <param name="title">Spreadsheet başlığı</param>
    /// <returns>Oluşturulan spreadsheet bilgisi</returns>
    Task<GoogleSheetsApiResponse<SpreadsheetInfo>> CreateSpreadsheetAsync(string title);

    /// <summary>
    /// Belirtilen spreadsheet'in bilgilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Spreadsheet bilgisi</returns>
    Task<GoogleSheetsApiResponse<SpreadsheetInfo>> GetSpreadsheetInfoAsync(string spreadsheetId);

    /// <summary>
    /// Servisin başlatılıp başlatılmadığını kontrol eder
    /// </summary>
    /// <returns>Servis durumu</returns>
    bool IsServiceInitialized();

    /// <summary>
    /// Belirtilen sheet'in ilk satırını beklenen şablonla karşılaştırır
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="headerTemplate">Beklenen başlık şablonu (| ile ayrılmış)</param>
    /// <returns>Başlık kontrol sonucu</returns>
    Task<GoogleSheetsApiResponse<HeaderValidationResult>> ValidateHeaderAsync(string spreadsheetId, string sheetName, string headerTemplate);

    /// <summary>
    /// Belirtilen sheet'in ilk satırını alır
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>İlk satır verisi</returns>
    Task<GoogleSheetsApiResponse<List<string>>> GetFirstRowAsync(string spreadsheetId, string sheetName);

    /// <summary>
    /// Belirtilen sheet'in ilk satırını günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="headerTemplate">Yeni başlık şablonu (| ile ayrılmış)</param>
    /// <returns>İşlem sonucu</returns>
    Task<GoogleSheetsApiResponse<bool>> UpdateHeaderAsync(string spreadsheetId, string sheetName, string headerTemplate);

    /// <summary>
    /// Başlık şablonunu parse eder
    /// </summary>
    /// <param name="headerTemplate">Başlık şablonu (| ile ayrılmış)</param>
    /// <returns>Parse edilmiş başlık listesi</returns>
    List<string> ParseHeaderTemplate(string headerTemplate);

    /// <summary>
    /// Belirtilen sütundan değer alır
    /// </summary>
    /// <param name="row">Satır verisi</param>
    /// <param name="headers">Sütun başlıkları</param>
    /// <param name="columnName">Sütun adı</param>
    /// <returns>Sütun değeri</returns>
    string? GetColumnValue(List<string> row, List<string> headers, string columnName);
}