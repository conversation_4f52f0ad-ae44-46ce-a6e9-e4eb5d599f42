using Microsoft.EntityFrameworkCore;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Data;

/// <summary>
/// Dinamik şema desteği olan veritabanı bağlamı sınıfı
/// Projeye göre farklı şemalardan tablolara eri<PERSON>
/// </summary>
public class DynamicSchemaDbContext : DbContext
{
    public readonly string SchemaName;

    /// <summary>
    /// DynamicSchemaDbContext constructor
    /// </summary>
    /// <param name="options">DbContext seçenekleri</param>
    /// <param name="schemaName">Kullanılacak şema adı</param>
    public DynamicSchemaDbContext(DbContextOptions<DynamicSchemaDbContext> options, string schemaName) : base(options)
    {
        SchemaName = schemaName;
    }

    /// <summary>
    /// Dinamik şemadaki metinler tablosu
    /// </summary>
    public DbSet<Text> Texts { get; set; }

    /// <summary>
    /// Dinamik şemadaki terimler tablosu
    /// </summary>
    public DbSet<Term> Terms { get; set; }

    /// <summary>
    /// Dinamik şemadaki bağlamlar tablosu
    /// </summary>
    public DbSet<Context> Contexts { get; set; }

    /// <summary>
    /// Model yapılandırması - dinamik şema ile
    /// </summary>
    /// <param name="modelBuilder">Model oluşturucu</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Text tablosu yapılandırması - dinamik şema ile
        modelBuilder.Entity<Text>(entity =>
        {
            entity.ToTable("texts", SchemaName);
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.RowID).HasColumnName("row_id");
            entity.Property(e => e.Namespace).HasColumnName("namespace").HasMaxLength(255);
            entity.Property(e => e.Key).HasColumnName("key").HasMaxLength(500);
            entity.Property(e => e.En).HasColumnName("en");
            entity.Property(e => e.Tr).HasColumnName("tr");
            entity.Property(e => e.Lemma).HasColumnName("lemma");
            entity.Property(e => e.Note).HasColumnName("note");
            entity.Property(e => e.Status).HasColumnName("status").HasColumnType("TextStatus");
            entity.Property(e => e.Embedding).HasColumnName("embedding").HasColumnType("vector(768)");
        });

        // Term tablosu yapılandırması - dinamik şema ile
        modelBuilder.Entity<Term>(entity =>
        {
            entity.ToTable("terms", SchemaName);
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.RowId).HasColumnName("row_id");
            entity.Property(e => e.En).HasColumnName("en").IsRequired();
            entity.Property(e => e.Tr).HasColumnName("tr");
            entity.Property(e => e.Category).HasColumnName("category").HasMaxLength(255);
            entity.Property(e => e.Info).HasColumnName("info");
            entity.Property(e => e.Lemma).HasColumnName("lemma");
            entity.Property(e => e.Status).HasColumnName("status").HasColumnType("TermStatus");
            entity.Property(e => e.Embedding).HasColumnName("embedding").HasColumnType("vector(768)");
        });

        // Context tablosu yapılandırması - dinamik şema ile
        modelBuilder.Entity<Context>(entity =>
        {
            entity.ToTable("contexts", SchemaName);
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Category).HasColumnName("category").HasMaxLength(255).IsRequired();
            entity.Property(e => e.Title).HasColumnName("title").HasMaxLength(500).IsRequired();
            entity.Property(e => e.Content).HasColumnName("content").IsRequired();
            entity.Property(e => e.CombinedText).HasColumnName("combined_text");
            entity.Property(e => e.CombinedTsvector).HasColumnName("combined_tsvector");
            entity.Property(e => e.Embedding).HasColumnName("embedding").HasColumnType("vector(768)");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });
    }
}