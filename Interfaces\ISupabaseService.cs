using Supabase;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Supabase veritabanı işlemleri için servis arayüzü
/// </summary>
public interface ISupabaseService
{

    /// <summary>
    /// Supabase proje client'ını döndürür
    /// </summary>
    /// <param name="ProjectId">Proje kimliği</param>
    /// <returns>Yapılandırılmış Supabase client</returns>
    Task<Client> GetProjectClient(long ProjectId);

    /// <summary>
    /// Supabase proje client'ını ayarlar
    /// </summary>
    /// <param name="ProjectId">Proje kimliği</param>
    /// <returns>Asenkron işlem</returns>
    Task<Client> SetProjectClient(long ProjectId);

    /// <summary>
    /// Supabase client örneğini döndürür
    /// </summary>
    /// <returns>Yapılandırılmış Supabase client</returns>
    Client GetClient();

    /// <summary>
    /// Supabase bağlantısını başlatır
    /// </summary>
    /// <returns>Asenkron işlem</returns>
    Task InitializeAsync();

    /// <summary>
    /// Belirtilen tablodan veri çeker
    /// </summary>
    /// <typeparam name="T">Model tipi</typeparam>
    /// <returns>Tablo referansı</returns>
    Supabase.Interfaces.ISupabaseTable<T, Supabase.Realtime.RealtimeChannel> From<T>() where T : Supabase.Postgrest.Models.BaseModel, new();

    /// <summary>
    /// Edge Function'ı çağırır
    /// </summary>
    /// <param name="functionName">Çağrılacak Edge Function'ın adı</param>
    /// <param name="funcoptions">Edge Function çağrısı için seçenekler</param>
    /// <returns>Edge Function'dan dönen sonuç</returns>
    Task<T> InvokeEdgeFunctionAsync<T>(string functionName, Supabase.Functions.Client.InvokeFunctionOptions? funcoptions = null);

    /// <summary>
    /// Supabase database function'ını çalıştırır
    /// </summary>
    /// <param name="functionName">Çalıştırılacak database function'ın adı</param>
    /// <param name="parameters">Function'a gönderilecek parametreler (opsiyonel)</param>
    /// <returns>Database function'dan dönen sonuç</returns>
    Task<T> ExecuteDatabaseFunctionAsync<T>(string functionName, object? parameters = null);

    /// <summary>
    /// Main tablosundan belirli bir anahtarın değerini alır
    /// </summary>
    /// <param name="key">Aranacak anahtar</param>
    /// <returns>Anahtar değeri</returns>
    Task<string?> GetMainValueAsync(string key);


}