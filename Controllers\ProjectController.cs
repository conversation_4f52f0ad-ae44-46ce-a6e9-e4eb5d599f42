using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
namespace TranslationAgentServer.Controllers;

/// <summary>
/// Proje yönetimi API controller'ı
/// Proje CRUD operasyonları ve arama işlevlerini sağlar
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProjectController : ControllerBase
{
    private readonly IProjectService _projectService;
    private readonly IProcessService _processService;
    private readonly ISupabaseService _supabaseService;
    private readonly ILogger<ProjectController> _logger;

    public ProjectController(IProjectService projectService, IProcessService processService, ISupabaseService supabaseService, ILogger<ProjectController> logger)
    {
        _projectService = projectService;
        _processService = processService;
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Belirtilen projeye ait tüm işlemleri listeler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>İşlem listesi</returns>
    [HttpGet("{projectId}/processes")]
    public async Task<ActionResult<IEnumerable<Process>>> GetProjectProcesses(long projectId)
    {
        try
        {

            //Proje processlerini kontrol et
            var checkProcesses = await _supabaseService.ExecuteDatabaseFunctionAsync<JsonElement>("check_processes_timeout");

            // Supabase fonksiyonundan dönen JSON sonucunu ayrıştır ve logla
            if (checkProcesses.TryGetProperty("success", out var successElement) && successElement.ValueKind == JsonValueKind.True)
            {
                if (checkProcesses.TryGetProperty("message", out var messageElement) && messageElement.ValueKind == JsonValueKind.String)
                {
                    _logger.LogInformation("Proses kontrolü başarılı: {Message}", messageElement.GetString());
                }
                else
                {
                    _logger.LogWarning("Proses kontrolü başarılı ancak mesaj bulunamadı.");
                }
            }
            else
            {
                if (checkProcesses.TryGetProperty("message", out var messageElement) && messageElement.ValueKind == JsonValueKind.String)
                {
                    _logger.LogError("Proses kontrolü başarısız: {Message}", messageElement.GetString());
                }
                else
                {
                    _logger.LogError("Proses kontrolü başarısız ve hata mesajı bulunamadı.");
                }
            }


            _logger.LogInformation("Proje işlemleri isteniyor: {ProjectId}", projectId);

            var project = await _projectService.GetProjectByIdAsync(projectId);
            if (project == null)
            {
                return NotFound(new { message = "Proje bulunamadı" });
            }

            var processes = await _processService.GetProcessesByProjectIdAsync(projectId);
            return Ok(processes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje işlemleri getirilirken hata oluştu: {ProjectId}", projectId);
            return StatusCode(500, new { message = "İşlemler getirilirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Tüm projeleri listeler
    /// </summary>
    /// <returns>Proje listesi</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Project>>> GetAllProjects()
    {
        try
        {
            _logger.LogInformation("Tüm projeler istendi");
            var projects = await _projectService.GetAllProjectsAsync();
            return Ok(projects);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Projeler getirilirken hata oluştu");
            return StatusCode(500, new { message = "Projeler getirilirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Belirtilen ID'ye sahip projeyi getirir
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Proje bilgileri</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Project>> GetProject(long id)
    {
        try
        {
            _logger.LogInformation("Proje istendi: {ProjectId}", id);

            if (id <= 0)
            {
                return BadRequest(new { message = "Geçersiz proje ID'si" });
            }

            var project = await _projectService.GetProjectByIdAsync(id);

            if (project == null)
            {
                _logger.LogWarning("Proje bulunamadı: {ProjectId}", id);
                return NotFound(new { message = "Proje bulunamadı" });
            }

            return Ok(project);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje getirilirken hata oluştu: {ProjectId}", id);
            return StatusCode(500, new { message = "Proje getirilirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Yeni proje oluşturur
    /// </summary>
    /// <param name="projectDto">Proje bilgileri</param>
    /// <returns>Oluşturulan proje</returns>
    [HttpPost]
    public async Task<ActionResult<Project>> CreateProject([FromBody] ProjectDto projectDto)
    {
        try
        {
            _logger.LogInformation("Yeni proje oluşturma isteği: {ProjectName}", projectDto.Name);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Aynı isimde proje var mı kontrol et
            var existingProjects = await _projectService.SearchProjectsByNameAsync(projectDto.Name);
            if (existingProjects.Any(p => p.Name?.Equals(projectDto.Name, StringComparison.OrdinalIgnoreCase) == true))
            {
                return Conflict(new { message = "Bu isimde bir proje zaten mevcut" });
            }

            // Google Sheets ID benzersizliği kontrolü
            if (!string.IsNullOrEmpty(projectDto.SpreadsheetId))
            {
                var existingProject = await _projectService.GetProjectBySpreadsheetIdAsync(projectDto.SpreadsheetId);
                if (existingProject != null)
                {
                    return Conflict(new { message = "Bu Google Sheets ID'si zaten kullanılıyor" });
                }
            }

            var createdProject = await _projectService.CreateProjectAsync(projectDto);

            _logger.LogInformation("Proje başarıyla oluşturuldu: {ProjectId}", createdProject.Id);

            var schemaCreated = await _projectService.CreateSchemaAsync(createdProject);
            if (!schemaCreated)
            {
                _logger.LogError("Proje şeması oluşturulamadı: {ProjectId}", createdProject.Id);
                return StatusCode(500, new { message = "Proje şeması oluşturulamadı", error = "Schema creation failed" });
            }



            return CreatedAtAction(nameof(GetProject), new { id = createdProject.Id }, createdProject);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje oluşturulurken hata oluştu: {ProjectName}", projectDto.Name);
            return StatusCode(500, new { message = "Proje oluşturulurken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Mevcut projeyi günceller
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <param name="projectUpdateDto">Güncellenecek proje bilgileri</param>
    /// <returns>Güncellenmiş proje</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Project>> UpdateProject(long id, [FromBody] ProjectUpdateDto projectUpdateDto)
    {
        try
        {
            _logger.LogInformation("Proje güncelleme isteği: {ProjectId}", id);

            if (id <= 0)
            {
                return BadRequest(new { message = "Geçersiz proje ID'si" });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Proje adı benzersizliği kontrolü (eğer ad güncelleniyor ise)
            if (!string.IsNullOrEmpty(projectUpdateDto.Name))
            {
                var existingProjects = await _projectService.SearchProjectsByNameAsync(projectUpdateDto.Name);
                var duplicateProject = existingProjects.FirstOrDefault(p =>
                    p.Name?.Equals(projectUpdateDto.Name, StringComparison.OrdinalIgnoreCase) == true && p.Id != id);

                if (duplicateProject != null)
                {
                    return Conflict(new { message = "Bu isimde başka bir proje zaten mevcut" });
                }
            }

            // Google Sheets ID benzersizliği kontrolü (eğer güncelleniyor ise)
            if (!string.IsNullOrEmpty(projectUpdateDto.SpreadsheetId))
            {
                var existingProject = await _projectService.GetProjectBySpreadsheetIdAsync(projectUpdateDto.SpreadsheetId);
                if (existingProject != null && existingProject.Id != id)
                {
                    return Conflict(new { message = "Bu Google Sheets ID'si başka bir proje tarafından kullanılıyor" });
                }
            }

            var updatedProject = await _projectService.UpdateProjectAsync(id, projectUpdateDto);

            if (updatedProject == null)
            {
                _logger.LogWarning("Güncellenecek proje bulunamadı: {ProjectId}", id);
                return NotFound(new { message = "Proje bulunamadı" });
            }

            _logger.LogInformation("Proje başarıyla güncellendi: {ProjectId}", id);
            return Ok(updatedProject);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje güncellenirken hata oluştu: {ProjectId}", id);
            return StatusCode(500, new { message = "Proje güncellenirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Projeyi siler
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Silme işleminin sonucu</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteProject(long id)
    {
        try
        {
            _logger.LogInformation("Proje silme isteği: {ProjectId}", id);

            if (id <= 0)
            {
                return BadRequest(new { message = "Geçersiz proje ID'si" });
            }

            var deleted = await _projectService.DeleteProjectAsync(id);

            if (!deleted)
            {
                _logger.LogWarning("Silinecek proje bulunamadı: {ProjectId}", id);
                return NotFound(new { message = "Proje bulunamadı" });
            }

            _logger.LogInformation("Proje başarıyla silindi: {ProjectId}", id);
            return Ok(new { message = "Proje başarıyla silindi" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje silinirken hata oluştu: {ProjectId}", id);
            return StatusCode(500, new { message = "Proje silinirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Proje adına göre arama yapar
    /// </summary>
    /// <param name="name">Aranacak proje adı</param>
    /// <returns>Eşleşen projeler</returns>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<Project>>> SearchProjects([FromQuery][Required] string name)
    {
        try
        {
            _logger.LogInformation("Proje arama isteği: {SearchTerm}", name);

            if (string.IsNullOrWhiteSpace(name))
            {
                return BadRequest(new { message = "Arama terimi boş olamaz" });
            }

            var projects = await _projectService.SearchProjectsByNameAsync(name);
            return Ok(projects);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje aranırken hata oluştu: {SearchTerm}", name);
            return StatusCode(500, new { message = "Proje aranırken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Google Sheets ID'sine göre proje bulur
    /// </summary>
    /// <param name="spreadsheetId">Google Sheets ID</param>
    /// <returns>Proje bilgileri</returns>
    [HttpGet("by-spreadsheet/{spreadsheetId}")]
    public async Task<ActionResult<Project>> GetProjectBySpreadsheetId(string spreadsheetId)
    {
        try
        {
            _logger.LogInformation("Google Sheets ID ile proje arama: {SpreadsheetId}", spreadsheetId);

            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest(new { message = "Google Sheets ID boş olamaz" });
            }

            var project = await _projectService.GetProjectBySpreadsheetIdAsync(spreadsheetId);

            if (project == null)
            {
                _logger.LogWarning("Google Sheets ID ile proje bulunamadı: {SpreadsheetId}", spreadsheetId);
                return NotFound(new { message = "Proje bulunamadı" });
            }

            return Ok(project);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Sheets ID ile proje aranırken hata oluştu: {SpreadsheetId}", spreadsheetId);
            return StatusCode(500, new { message = "Proje aranırken bir hata oluştu", error = ex.Message });
        }
    }

}