using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Google Sheets API işlemleri için controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class GoogleSheetsController : ControllerBase
{
    private readonly IGoogleSheetsService _googleSheetsService;
    private readonly ILogger<GoogleSheetsController> _logger;

    /// <summary>
    /// GoogleSheetsController constructor
    /// </summary>
    /// <param name="googleSheetsService">Google Sheets servisi</param>
    /// <param name="logger">Logger instance</param>
    public GoogleSheetsController(IGoogleSheetsService googleSheetsService, ILogger<GoogleSheetsController> logger)
    {
        _googleSheetsService = googleSheetsService;
        _logger = logger;
    }

    /// <summary>
    /// Service account JSON ile Google Sheets servisini başlatır
    /// </summary>
    /// <param name="request">Service account bilgileri</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPost("initialize")]
    public async Task<IActionResult> InitializeService([FromBody] GoogleServiceAccountRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.ServiceAccountJson))
            {
                return BadRequest("Service account JSON gereklidir.");
            }

            var result = await _googleSheetsService.InitializeServiceAsync(request.ServiceAccountJson);
            
            if (result.Success)
            {
                return Ok(new { message = "Google Sheets servisi başarıyla başlatıldı.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Sheets servisi başlatılırken hata oluştu.");
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Servis durumunu kontrol eder
    /// </summary>
    /// <returns>Servis durumu</returns>
    [HttpGet("status")]
    public IActionResult GetServiceStatus()
    {
        var isInitialized = _googleSheetsService.IsServiceInitialized();
        return Ok(new { initialized = isInitialized, message = isInitialized ? "Servis aktif" : "Servis başlatılmamış" });
    }

    /// <summary>
    /// Kullanıcının erişebildiği tüm spreadsheet'leri listeler
    /// </summary>
    /// <returns>Spreadsheet listesi</returns>
    [HttpGet("spreadsheets")]
    public async Task<IActionResult> GetSpreadsheets()
    {
        try
        {
            var result = await _googleSheetsService.GetSpreadsheetsAsync();
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet'ler listelenirken hata oluştu.");
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen spreadsheet'in bilgilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Spreadsheet bilgisi</returns>
    [HttpGet("spreadsheets/{spreadsheetId}")]
    public async Task<IActionResult> GetSpreadsheetInfo(string spreadsheetId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            var result = await _googleSheetsService.GetSpreadsheetInfoAsync(spreadsheetId);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet bilgisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Yeni bir spreadsheet oluşturur
    /// </summary>
    /// <param name="title">Spreadsheet başlığı</param>
    /// <returns>Oluşturulan spreadsheet bilgisi</returns>
    [HttpPost("spreadsheets")]
    public async Task<IActionResult> CreateSpreadsheet([FromBody] string title)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(title))
            {
                return BadRequest("Spreadsheet başlığı gereklidir.");
            }

            var result = await _googleSheetsService.CreateSpreadsheetAsync(title);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet oluşturulurken hata oluştu. Title: {Title}", title);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen spreadsheet'in tüm sheet'lerini listeler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Sheet listesi</returns>
    [HttpGet("spreadsheets/{spreadsheetId}/sheets")]
    public async Task<IActionResult> GetSheets(string spreadsheetId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            var result = await _googleSheetsService.GetSheetsAsync(spreadsheetId);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet'ler listelenirken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Yeni bir sheet oluşturur
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="createRequest">Sheet oluşturma isteği</param>
    /// <returns>Oluşturulan sheet bilgisi</returns>
    [HttpPost("spreadsheets/{spreadsheetId}/sheets")]
    public async Task<IActionResult> CreateSheet(string spreadsheetId, [FromBody] CreateSheetRequest createRequest)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            if (string.IsNullOrWhiteSpace(createRequest.Title))
            {
                return BadRequest("Sheet başlığı gereklidir.");
            }

            var result = await _googleSheetsService.CreateSheetAsync(spreadsheetId, createRequest);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet oluşturulurken hata oluştu. SpreadsheetId: {SpreadsheetId}, Title: {Title}", 
                spreadsheetId, createRequest.Title);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen sheet'i siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Silinecek sheet ID'si</param>
    /// <returns>İşlem sonucu</returns>
    [HttpDelete("spreadsheets/{spreadsheetId}/sheets/{sheetId}")]
    public async Task<IActionResult> DeleteSheet(string spreadsheetId, int sheetId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            var result = await _googleSheetsService.DeleteSheetAsync(spreadsheetId, sheetId);
            
            if (result.Success)
            {
                return Ok(new { message = "Sheet başarıyla silindi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet silinirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetId: {SheetId}", 
                spreadsheetId, sheetId);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen sheet'in tüm verilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>Sheet verisi</returns>
    [HttpGet("spreadsheets/{spreadsheetId}/sheets/{sheetName}/data")]
    public async Task<IActionResult> GetSheetData(string spreadsheetId, string sheetName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(sheetName))
            {
                return BadRequest("Spreadsheet ID ve Sheet adı gereklidir.");
            }

            var result = await _googleSheetsService.GetSheetDataAsync(spreadsheetId, sheetName);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet verisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen aralıktaki verileri getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Veri aralığı (örn: Sheet1!A1:C10)</param>
    /// <returns>Aralık verisi</returns>
    [HttpGet("spreadsheets/{spreadsheetId}/range")]
    public async Task<IActionResult> GetRangeData(string spreadsheetId, [FromQuery] string range)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(range))
            {
                return BadRequest("Spreadsheet ID ve aralık gereklidir.");
            }

            var result = await _googleSheetsService.GetRangeDataAsync(spreadsheetId, range);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Aralık verisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}, Range: {Range}", 
                spreadsheetId, range);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen sheet'e yeni satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowData">Eklenecek satır verisi</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPost("spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows")]
    public async Task<IActionResult> AddRow(string spreadsheetId, string sheetName, [FromBody] SheetRowData rowData)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(sheetName))
            {
                return BadRequest("Spreadsheet ID ve Sheet adı gereklidir.");
            }

            if (rowData?.Values == null || !rowData.Values.Any())
            {
                return BadRequest("Satır verisi gereklidir.");
            }

            var result = await _googleSheetsService.AddRowAsync(spreadsheetId, sheetName, rowData);
            
            if (result.Success)
            {
                return Ok(new { message = "Satır başarıyla eklendi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satır eklenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen sheet'e birden fazla satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowsData">Eklenecek satırlar</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPost("spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows/batch")]
    public async Task<IActionResult> AddRows(string spreadsheetId, string sheetName, [FromBody] List<SheetRowData> rowsData)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(sheetName))
            {
                return BadRequest("Spreadsheet ID ve Sheet adı gereklidir.");
            }

            if (rowsData == null || !rowsData.Any())
            {
                return BadRequest("Satır verileri gereklidir.");
            }

            var result = await _googleSheetsService.AddRowsAsync(spreadsheetId, sheetName, rowsData);
            
            if (result.Success)
            {
                return Ok(new { message = $"{rowsData.Count} satır başarıyla eklendi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satırlar eklenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen satırı siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="rowIndex">Silinecek satır indeksi (0-based)</param>
    /// <returns>İşlem sonucu</returns>
    [HttpDelete("spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows/{rowIndex}")]
    public async Task<IActionResult> DeleteRow(string spreadsheetId, int sheetId, int rowIndex)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            if (rowIndex < 0)
            {
                return BadRequest("Satır indeksi 0 veya daha büyük olmalıdır.");
            }

            var result = await _googleSheetsService.DeleteRowAsync(spreadsheetId, sheetId, rowIndex);
            
            if (result.Success)
            {
                return Ok(new { message = "Satır başarıyla silindi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satır silinirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetId: {SheetId}, RowIndex: {RowIndex}", 
                spreadsheetId, sheetId, rowIndex);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen satır aralığını siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="startRowIndex">Başlangıç satır indeksi (0-based)</param>
    /// <param name="endRowIndex">Bitiş satır indeksi (0-based, dahil değil)</param>
    /// <returns>İşlem sonucu</returns>
    [HttpDelete("spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows")]
    public async Task<IActionResult> DeleteRows(string spreadsheetId, int sheetId, [FromQuery] int startRowIndex, [FromQuery] int endRowIndex)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            if (startRowIndex < 0 || endRowIndex <= startRowIndex)
            {
                return BadRequest("Geçersiz satır aralığı.");
            }

            var result = await _googleSheetsService.DeleteRowsAsync(spreadsheetId, sheetId, startRowIndex, endRowIndex);
            
            if (result.Success)
            {
                return Ok(new { message = $"{endRowIndex - startRowIndex} satır başarıyla silindi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satırlar silinirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetId: {SheetId}, StartRow: {StartRow}, EndRow: {EndRow}", 
                spreadsheetId, sheetId, startRowIndex, endRowIndex);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen hücreyi günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Hücre aralığı (örn: Sheet1!A1)</param>
    /// <param name="value">Yeni değer</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPut("spreadsheets/{spreadsheetId}/cell")]
    public async Task<IActionResult> UpdateCell(string spreadsheetId, [FromQuery] string range, [FromBody] string value)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(range))
            {
                return BadRequest("Spreadsheet ID ve aralık gereklidir.");
            }

            var result = await _googleSheetsService.UpdateCellAsync(spreadsheetId, range, value ?? string.Empty);
            
            if (result.Success)
            {
                return Ok(new { message = "Hücre başarıyla güncellendi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Hücre güncellenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, Range: {Range}", 
                spreadsheetId, range);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen aralığı günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Aralık (örn: Sheet1!A1:C3)</param>
    /// <param name="values">Yeni değerler</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPut("spreadsheets/{spreadsheetId}/range")]
    public async Task<IActionResult> UpdateRange(string spreadsheetId, [FromQuery] string range, [FromBody] List<List<string>> values)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(range))
            {
                return BadRequest("Spreadsheet ID ve aralık gereklidir.");
            }

            if (values == null || !values.Any())
            {
                return BadRequest("Değerler gereklidir.");
            }

            var result = await _googleSheetsService.UpdateRangeAsync(spreadsheetId, range, values);
            
            if (result.Success)
            {
                return Ok(new { message = "Aralık başarıyla güncellendi.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Aralık güncellenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, Range: {Range}", 
                spreadsheetId, range);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Toplu güncelleme yapar
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="batchRequest">Toplu güncelleme isteği</param>
    /// <returns>İşlem sonucu</returns>
    [HttpPut("spreadsheets/{spreadsheetId}/batch")]
    public async Task<IActionResult> BatchUpdate(string spreadsheetId, [FromBody] BatchUpdateRequest batchRequest)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            if (batchRequest?.Updates == null || !batchRequest.Updates.Any())
            {
                return BadRequest("Güncelleme verileri gereklidir.");
            }

            var result = await _googleSheetsService.BatchUpdateAsync(spreadsheetId, batchRequest);
            
            if (result.Success)
            {
                return Ok(new { message = $"{batchRequest.Updates.Count} güncelleme başarıyla yapıldı.", success = true });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu güncelleme yapılırken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen sheet'in ilk satırını beklenen şablonla karşılaştırır
    /// </summary>
    /// <param name="request">Başlık kontrol isteği</param>
    /// <returns>Başlık kontrol sonucu</returns>
    [HttpPost("validate-header")]
    public async Task<IActionResult> ValidateHeader([FromBody] ValidateHeaderRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.SpreadsheetId) || 
                string.IsNullOrWhiteSpace(request.SheetName) || 
                string.IsNullOrWhiteSpace(request.HeaderTemplate))
            {
                return BadRequest("Spreadsheet ID, Sheet adı ve başlık şablonu gereklidir.");
            }

            var result = await _googleSheetsService.ValidateHeaderAsync(request.SpreadsheetId, request.SheetName, request.HeaderTemplate);
            
            if (result.Success)
            {
                return Ok(result.Data);
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Başlık kontrol edilirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                request.SpreadsheetId, request.SheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Texts sayfasının sütun yapısını kontrol eder
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>Texts sütun kontrol sonucu</returns>
    [HttpGet("validate-texts-columns/{spreadsheetId}/{sheetName}")]
    public async Task<IActionResult> ValidateTextsColumns(string spreadsheetId, string sheetName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(sheetName))
            {
                return BadRequest("Spreadsheet ID ve Sheet adı gereklidir.");
            }

            // Supabase'den texts_columns verisini al
            var supabaseService = HttpContext.RequestServices.GetRequiredService<ISupabaseService>();
            var textsColumns = await supabaseService.GetMainValueAsync("texts_columns");

            if (string.IsNullOrWhiteSpace(textsColumns))
            {
                return BadRequest("Supabase'de 'texts_columns' verisi bulunamadı.");
            }

            var result = await _googleSheetsService.ValidateHeaderAsync(spreadsheetId, sheetName, textsColumns);
            
            if (result.Success)
            {
                return Ok(new 
                { 
                    isValid = result.Data?.IsValid ?? false,
                    message = result.Data?.IsValid == true ? "Texts sütun yapısı doğru." : result.Data?.ErrorMessage,
                    expectedColumns = result.Data?.ExpectedHeaders,
                    actualColumns = result.Data?.ActualHeaders,
                    missingColumns = result.Data?.MissingHeaders,
                    extraColumns = result.Data?.ExtraHeaders,
                    success = true
                });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Texts sütun kontrol edilirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Terms sayfasının sütun yapısını kontrol eder
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>Terms sütun kontrol sonucu</returns>
    [HttpGet("validate-terms-columns/{spreadsheetId}/{sheetName}")]
    public async Task<IActionResult> ValidateTermsColumns(string spreadsheetId, string sheetName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId) || string.IsNullOrWhiteSpace(sheetName))
            {
                return BadRequest("Spreadsheet ID ve Sheet adı gereklidir.");
            }

            // Supabase'den terms_columns verisini al
            var supabaseService = HttpContext.RequestServices.GetRequiredService<ISupabaseService>();
            var termsColumns = await supabaseService.GetMainValueAsync("terms_columns");

            if (string.IsNullOrWhiteSpace(termsColumns))
            {
                return BadRequest("Supabase'de 'terms_columns' verisi bulunamadı.");
            }

            var result = await _googleSheetsService.ValidateHeaderAsync(spreadsheetId, sheetName, termsColumns);
            
            if (result.Success)
            {
                return Ok(new 
                { 
                    isValid = result.Data?.IsValid ?? false,
                    message = result.Data?.IsValid == true ? "Terms sütun yapısı doğru." : result.Data?.ErrorMessage,
                    expectedColumns = result.Data?.ExpectedHeaders,
                    actualColumns = result.Data?.ActualHeaders,
                    missingColumns = result.Data?.MissingHeaders,
                    extraColumns = result.Data?.ExtraHeaders,
                    success = true
                });
            }
            
            return BadRequest(new { message = result.ErrorMessage, success = false });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Terms sütun kontrol edilirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }

    /// <summary>
    /// Belirtilen spreadsheet'e "Terimce" adında yeni bir sheet oluşturur ve terms_columns sütunlarını ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı (opsiyonel, varsayılan: "Terimce")</param>
    /// <returns>Oluşturulan Terimce sheet bilgisi</returns>
    [HttpPost("spreadsheets/{spreadsheetId}/create-terimce-sheet")]
    public async Task<IActionResult> CreateTerimceSheet(string spreadsheetId, [FromQuery] string? sheetName = "Terimce")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                return BadRequest("Spreadsheet ID gereklidir.");
            }

            // Varsayılan sheet adını ayarla
            if (string.IsNullOrWhiteSpace(sheetName))
            {
                sheetName = "Terimce";
            }

            // Önce sheet'in zaten var olup olmadığını kontrol et
            var sheetsResult = await _googleSheetsService.GetSheetsAsync(spreadsheetId);
            if (sheetsResult.Success && sheetsResult.Data != null)
            {
                var existingSheet = sheetsResult.Data.FirstOrDefault(s => 
                    string.Equals(s.Title, sheetName, StringComparison.OrdinalIgnoreCase));
                
                if (existingSheet != null)
                {
                    return Conflict(new 
                    { 
                        message = $"'{sheetName}' adında bir sheet zaten mevcut.",
                        existingSheetInfo = existingSheet,
                        success = false
                    });
                }
            }

            // Supabase'den terms_columns verisini al
            var supabaseService = HttpContext.RequestServices.GetRequiredService<ISupabaseService>();
            var termsColumns = await supabaseService.GetMainValueAsync("terms_columns");

            if (string.IsNullOrWhiteSpace(termsColumns))
            {
                return BadRequest("Supabase'de 'terms_columns' verisi bulunamadı.");
            }

            // terms_columns'u parse et
            var columnHeaders = _googleSheetsService.ParseHeaderTemplate(termsColumns);

            if (!columnHeaders.Any())
            {
                return BadRequest("terms_columns verisi parse edilemedi veya boş.");
            }

            // Yeni sheet oluştur
            var createSheetRequest = new CreateSheetRequest
            {
                Title = sheetName,
                RowCount = 1000,
                ColumnCount = Math.Max(columnHeaders.Count, 10) // En az 10 sütun, gerekirse daha fazla
            };

            var createResult = await _googleSheetsService.CreateSheetAsync(spreadsheetId, createSheetRequest);

            if (!createResult.Success)
            {
                return BadRequest(new { message = $"Sheet oluşturulamadı: {createResult.ErrorMessage}", success = false });
            }

            // İlk satıra başlık sütunlarını ekle
            var headerRowData = new SheetRowData
            {
                Values = columnHeaders
            };

            var addHeaderResult = await _googleSheetsService.AddRowAsync(spreadsheetId, sheetName, headerRowData);

            if (!addHeaderResult.Success)
            {
                _logger.LogWarning("Sheet oluşturuldu ancak başlık satırı eklenemedi. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}, Error: {Error}", 
                    spreadsheetId, sheetName, addHeaderResult.ErrorMessage);
                
                return Ok(new 
                { 
                    message = $"'{sheetName}' sheet'i oluşturuldu ancak başlık satırı eklenemedi: {addHeaderResult.ErrorMessage}",
                    sheetInfo = createResult.Data,
                    columnsAdded = false,
                    expectedColumns = columnHeaders,
                    success = true
                });
            }

            _logger.LogInformation("Terimce sheet başarıyla oluşturuldu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}, Columns: {Columns}", 
                spreadsheetId, sheetName, string.Join(", ", columnHeaders));

            return Ok(new 
            { 
                message = $"'{sheetName}' sheet'i başarıyla oluşturuldu ve sütun başlıkları eklendi.",
                sheetInfo = createResult.Data,
                columnsAdded = true,
                addedColumns = columnHeaders,
                termsColumnsTemplate = termsColumns,
                success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Terimce sheet oluşturulurken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", 
                spreadsheetId, sheetName);
            return StatusCode(500, new { message = "İç sunucu hatası.", success = false });
        }
    }
}