using System.Collections.Concurrent;
using TranslationAgentServer.Models;
using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Services;

/// <summary>
/// <PERSON>lik doğrulama işlemlerini yöneten servis sınıfı
/// </summary>
public class AuthService : IAuthService
{
    private readonly ISupabaseService _supabaseService;
    private readonly ILogger<AuthService> _logger;
    private readonly IConfiguration _configuration;
    
    // Aktif session'ları bellekte tutmak için thread-safe collection
    private static readonly ConcurrentDictionary<string, DateTime> _activeSessions = new();
    
    // Session süresi (30 dakika)
    private static readonly TimeSpan SessionTimeout = TimeSpan.FromMinutes(30);

    /// <summary>
    /// AuthService constructor
    /// </summary>
    /// <param name="supabaseService">Supabase servis instance</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="configuration">Configuration instance</param>
    public AuthService(ISupabaseService supabaseService, ILogger<AuthService> logger, IConfiguration configuration)
    {
        _supabaseService = supabaseService;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Şifre ile giriş yapar
    /// </summary>
    /// <param name="password">Giriş şifresi</param>
    /// <returns>Login sonucu</returns>
    public async Task<LoginResponse> LoginAsync(string password)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(password))
            {
                return new LoginResponse
                {
                    Success = false,
                    Message = "Şifre boş olamaz"
                };
            }

            // Supabase'den password değerini al
            var response = await _supabaseService
                .From<MainData>()
                .Where(x => x.Name == "password")
                .Single();

            if (response?.Value == null)
            {
                _logger.LogWarning("Password değeri veritabanında bulunamadı");
                return new LoginResponse
                {
                    Success = false,
                    Message = "Sistem hatası"
                };
            }

            // Şifre kontrolü
            if (response.Value != password)
            {
                _logger.LogWarning("Hatalı şifre girişi yapıldı");
                return new LoginResponse
                {
                    Success = false,
                    Message = "Hatalı şifre"
                };
            }

            // Başarılı giriş - session oluştur
            var sessionId = Guid.NewGuid().ToString();
            _activeSessions[sessionId] = DateTime.UtcNow.Add(SessionTimeout);

            _logger.LogInformation("Başarılı giriş yapıldı. Session ID: {SessionId}", sessionId);

            return new LoginResponse
            {
                Success = true,
                Message = "Giriş başarılı",
                SessionId = sessionId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login işlemi sırasında hata oluştu");
            return new LoginResponse
            {
                Success = false,
                Message = "Sistem hatası"
            };
        }
    }

    /// <summary>
    /// Session'ın geçerli olup olmadığını kontrol eder
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <returns>Session geçerli mi</returns>
    public async Task<bool> ValidateSessionAsync(string sessionId)
    {
        await Task.CompletedTask; // Async method için
        
        // Development ortamında session doğrulamayı atla
        var skipAuth = _configuration.GetValue<bool>("Development:SkipAuthentication", false);
        if (skipAuth)
        {
            _logger.LogInformation("Development ortamında session doğrulama atlandı: {SessionId}", sessionId);
            return true;
        }
        
        if (string.IsNullOrWhiteSpace(sessionId))
            return false;

        // Süresi dolmuş session'ları temizle
        CleanExpiredSessions();

        // Session kontrolü
        if (_activeSessions.TryGetValue(sessionId, out var expiry))
        {
            if (DateTime.UtcNow < expiry)
            {
                // Session'ı yenile
                _activeSessions[sessionId] = DateTime.UtcNow.Add(SessionTimeout);
                return true;
            }
            else
            {
                // Süresi dolmuş session'ı kaldır
                _activeSessions.TryRemove(sessionId, out _);
            }
        }

        return false;
    }

    /// <summary>
    /// Session'ı sonlandırır
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <returns>Logout başarılı mı</returns>
    public async Task<bool> LogoutAsync(string sessionId)
    {
        await Task.CompletedTask; // Async method için
        
        if (string.IsNullOrWhiteSpace(sessionId))
            return false;

        var removed = _activeSessions.TryRemove(sessionId, out _);
        
        if (removed)
        {
            _logger.LogInformation("Session sonlandırıldı. Session ID: {SessionId}", sessionId);
        }

        return removed;
    }

    /// <summary>
    /// Süresi dolmuş session'ları temizler
    /// </summary>
    private static void CleanExpiredSessions()
    {
        var now = DateTime.UtcNow;
        var expiredSessions = _activeSessions
            .Where(kvp => now >= kvp.Value)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var sessionId in expiredSessions)
        {
            _activeSessions.TryRemove(sessionId, out _);
        }
    }
}