using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Middleware;

/// <summary>
/// Kimlik doğrulama kontrolü yapan middleware sınıfı
/// </summary>
public class AuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuthenticationMiddleware> _logger;
    private readonly IConfiguration _configuration;
    
    // Kimlik doğrulama gerektirmeyen endpoint'ler
    private static readonly HashSet<string> PublicEndpoints = new(StringComparer.OrdinalIgnoreCase)
    {
        "/api/auth/login",
        "/api/auth/status",
        "/health",
        "/health/detailed",
        "/swagger",
        "/swagger/index.html"
    };

    /// <summary>
    /// AuthenticationMiddleware constructor
    /// </summary>
    /// <param name="next">Sonraki middleware</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="configuration">Configuration instance</param>
    public AuthenticationMiddleware(RequestDelegate next, ILogger<AuthenticationMiddleware> logger, IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Middleware'in ana işlevi
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <param name="authService">Authentication servis</param>
    /// <returns>Task</returns>
    public async Task InvokeAsync(HttpContext context, IAuthService authService)
    {
        var path = context.Request.Path.Value ?? string.Empty;
        
        // Development ortamında kimlik doğrulamayı atla
        var skipAuth = _configuration.GetValue<bool>("Development:SkipAuthentication", false);
        if (skipAuth)
        {
            _logger.LogInformation("Development ortamında kimlik doğrulama atlandı: {Path}", path);
            await _next(context);
            return;
        }
        
        // Public endpoint kontrolü
        if (IsPublicEndpoint(path))
        {
            await _next(context);
            return;
        }

        // Session kontrolü
        var sessionId = context.Request.Cookies["SessionId"];
        
        if (string.IsNullOrEmpty(sessionId))
        {
            _logger.LogWarning("Yetkisiz erişim denemesi: {Path} - Session ID bulunamadı", path);
            await WriteUnauthorizedResponse(context, "Session bulunamadı");
            return;
        }

        var isValidSession = await authService.ValidateSessionAsync(sessionId);
        
        if (!isValidSession)
        {
            _logger.LogWarning("Yetkisiz erişim denemesi: {Path} - Geçersiz session", path);
            
            // Geçersiz session cookie'sini temizle
            context.Response.Cookies.Delete("SessionId");
            
            await WriteUnauthorizedResponse(context, "Session süresi dolmuş veya geçersiz");
            return;
        }

        // Session geçerli, devam et
        await _next(context);
    }

    /// <summary>
    /// Endpoint'in public olup olmadığını kontrol eder
    /// </summary>
    /// <param name="path">Request path</param>
    /// <returns>Public endpoint mi</returns>
    private static bool IsPublicEndpoint(string path)
    {
        // Exact match kontrolü
        if (PublicEndpoints.Contains(path))
            return true;

        // Swagger ile başlayan path'ler
        if (path.StartsWith("/swagger", StringComparison.OrdinalIgnoreCase))
            return true;

        // Static dosyalar (css, js, images vb.)
        var extension = Path.GetExtension(path);
        if (!string.IsNullOrEmpty(extension))
        {
            var staticExtensions = new[] { ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg" };
            if (staticExtensions.Contains(extension, StringComparer.OrdinalIgnoreCase))
                return true;
        }

        return false;
    }

    /// <summary>
    /// Yetkisiz erişim yanıtı yazar
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <param name="message">Hata mesajı</param>
    /// <returns>Task</returns>
    private static async Task WriteUnauthorizedResponse(HttpContext context, string message)
    {
        context.Response.StatusCode = 401;
        context.Response.ContentType = "application/json";
        
        var response = new
        {
            error = "Unauthorized",
            message = message,
            timestamp = DateTime.UtcNow
        };
        
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
}