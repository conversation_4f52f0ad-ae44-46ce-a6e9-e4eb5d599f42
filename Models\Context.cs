using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace TranslationAgentServer.Models;

/// <summary>
/// Context veri modeli
/// Supabase contexts tablosunu temsil eder
/// </summary>
[Table("contexts")]
public class Context
{
    /// <summary>
    /// Benz<PERSON>iz kimlik
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public long Id { get; set; }

    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [Column("category")]
    [Required]
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [Column("title")]
    [Required]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    [Column("content")]
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Birleştirilmiş metin (otomatik oluşturulan)
    /// </summary>
    [Column("combined_text")]
    public string? CombinedText { get; set; }

    /// <summary>
    /// Birleştirilmiş tsvector (otomatik oluşturulan)
    /// </summary>
    [Column("combined_tsvector")]
    public string? CombinedTsvector { get; set; }

    /// <summary>
    /// Embedding vektörü
    /// </summary>
    [Column("embedding")]
    public float[]? Embedding { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Güncellenme tarihi
    /// </summary>
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Context oluşturma DTO'su
/// </summary>
public class ContextCreateDto
{
    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [Required(ErrorMessage = "Kategori alanı zorunludur.")]
    [StringLength(255, ErrorMessage = "Kategori en fazla 255 karakter olabilir.")]
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [Required(ErrorMessage = "Başlık alanı zorunludur.")]
    [StringLength(500, ErrorMessage = "Başlık en fazla 500 karakter olabilir.")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    [Required(ErrorMessage = "İçerik alanı zorunludur.")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Embedding vektörü (opsiyonel)
    /// </summary>
    public float[]? Embedding { get; set; }
}

/// <summary>
/// Context güncelleme DTO'su
/// </summary>
public class ContextUpdateDto
{
    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [StringLength(255, ErrorMessage = "Kategori en fazla 255 karakter olabilir.")]
    public string? Category { get; set; }

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [StringLength(500, ErrorMessage = "Başlık en fazla 500 karakter olabilir.")]
    public string? Title { get; set; }

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// Embedding vektörü
    /// </summary>
    public float[]? Embedding { get; set; }
}

/// <summary>
/// Context istatistikleri modeli
/// </summary>
public class ContextStatistics
{
    /// <summary>
    /// Toplam context sayısı
    /// </summary>
    public int TotalContexts { get; set; }

    /// <summary>
    /// Embedding'i olan context sayısı
    /// </summary>
    public int ContextsWithEmbedding { get; set; }

    /// <summary>
    /// Kategorilere göre dağılım
    /// </summary>
    public Dictionary<string, int> CategoryDistribution { get; set; } = new();

    /// <summary>
    /// En son oluşturulan context tarihi
    /// </summary>
    public DateTime? LastCreatedAt { get; set; }

    /// <summary>
    /// En son güncellenen context tarihi
    /// </summary>
    public DateTime? LastUpdatedAt { get; set; }
}