using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Controllers
{
    /// <summary>
    /// Terim yönetimi için RESTful API controller'ı.
    /// yeni_schema.terms tablosu ile CRUD ve arama işlemlerini sağlar.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TermController : ControllerBase
    {
        private readonly ITermService _termService;
        private readonly ILogger<TermController> _logger;

        public TermController(ITermService termService, ILogger<TermController> logger)
        {
            _termService = termService;
            _logger = logger;
        }

        /// <summary>
        /// Belirtilen şemadaki tüm terimleri sayfalanmış olarak getirir
        /// </summary>
        /// <param name="projectID"><PERSON><PERSON> kim<PERSON></param>
        /// <param name="page"><PERSON><PERSON> numa<PERSON> (varsayılan: 1)</param>
        /// <param name="pageSize"><PERSON><PERSON> boyutu (varsayılan: 50)</param>
        /// <param name="enFilter">İngilizce terim filtresi</param>
        /// <param name="trFilter">Türkçe terim filtresi</param>
        /// <param name="categoryFilter">Kategori filtresi</param>
        /// <param name="infoFilter">Bilgi filtresi</param>
        /// <param name="statusFilter">Durum filtresi</param>
        /// <returns>Sayfalanmış terim listesi</returns>
        [HttpGet("{projectID}")]
        public async Task<ActionResult<List<Term>>> GetAllTerms(long projectID,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? enFilter = null,
            [FromQuery] string? trFilter = null,
            [FromQuery] string? categoryFilter = null,
            [FromQuery] string? infoFilter = null,
            [FromQuery] TermStatus? statusFilter = null)
        {
            try
            {
                var result = await _termService.GetAllTermsAsync(projectID, page, pageSize,
                    enFilter, trFilter, categoryFilter, infoFilter, statusFilter);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tüm terimler getirilirken hata oluştu. Proje: {ProjectID}", projectID);
                return StatusCode(500, new { message = "Terimler getirilirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// ID'ye göre terim getirir
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Terim ID'si</param>
        /// <returns>Bulunan terim</returns>
        [HttpGet("{projectID}/{id}")]
        public async Task<ActionResult<Term>> GetTermById(long projectID, long id)
        {
            try
            {
                var term = await _termService.GetTermByIdAsync(id, projectID);
                if (term == null)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı" });
                }

                return Ok(term);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim getirilirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim getirilirken bir hata oluştu", error = ex.Message });
            }
        }


        /// <summary>
        /// Terim arama yapar
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Arama sonuçları</returns>
        [HttpGet("{projectID}/search")]
        public async Task<ActionResult<List<Term>>> SearchTerms(long projectID, [FromQuery] string searchTerm, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(new { message = "Arama terimi boş olamaz" });
                }

                var result = await _termService.SearchTermsAsync(searchTerm, projectID, page, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim arama işlemi sırasında hata oluştu. SearchTerm: {SearchTerm}, Proje: {ProjectID}", searchTerm, projectID);
                return StatusCode(500, new { message = "Arama işlemi sırasında bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Full-text search yapar
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="query">Arama sorgusu</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Arama sonuçları</returns>
        [HttpGet("{projectID}/fulltext-search")]
        public async Task<ActionResult<List<Term>>> FullTextSearch(long projectID, [FromQuery] string query, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    return BadRequest(new { message = "Arama sorgusu boş olamaz" });
                }

                var result = await _termService.FullTextSearchAsync(query, projectID, page, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Full-text search sırasında hata oluştu. Query: {Query}, Proje: {ProjectID}", query, projectID);
                return StatusCode(500, new { message = "Arama işlemi sırasında bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// İngilizce terime göre arama yapar
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="englishTerm">İngilizce terim</param>
        /// <returns>Bulunan terimler</returns>
        [HttpGet("{projectID}/find/english/{englishTerm}")]
        public async Task<ActionResult<List<Term>>> FindByEnglishTerm(long projectID, string englishTerm)
        {
            try
            {
                var terms = await _termService.FindByEnglishTermAsync(englishTerm, projectID);
                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "İngilizce terim araması sırasında hata oluştu. Term: {Term}, Proje: {ProjectID}", englishTerm, projectID);
                return StatusCode(500, new { message = "Arama işlemi sırasında bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Türkçe terime göre arama yapar
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="turkishTerm">Türkçe terim</param>
        /// <returns>Bulunan terimler</returns>
        [HttpGet("{projectID}/find/turkish/{turkishTerm}")]
        public async Task<ActionResult<List<Term>>> FindByTurkishTerm(long projectID, string turkishTerm)
        {
            try
            {
                var terms = await _termService.FindByTurkishTermAsync(turkishTerm, projectID);
                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Türkçe terim araması sırasında hata oluştu. Term: {Term}, Proje: {ProjectID}", turkishTerm, projectID);
                return StatusCode(500, new { message = "Arama işlemi sırasında bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Lemma'ya göre arama yapar
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="lemma">Lemma</param>
        /// <returns>Bulunan terimler</returns>
        [HttpGet("{projectID}/find/lemma/{lemma}")]
        public async Task<ActionResult<List<Term>>> FindByLemma(long projectID, string lemma)
        {
            try
            {
                var terms = await _termService.FindByLemmaAsync(lemma, projectID);
                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lemma araması sırasında hata oluştu. Lemma: {Lemma}, Proje: {ProjectID}", lemma, projectID);
                return StatusCode(500, new { message = "Arama işlemi sırasında bir hata oluştu", error = ex.Message });
            }
        }
        /// <summary>
        /// Terim istatistiklerini getirir
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <returns>İstatistik bilgileri</returns>
        [HttpGet("{projectID}/statistics")]
        public async Task<ActionResult<TermStatistics>> GetTermStatistics(long projectID)
        {
            try
            {
                var statistics = await _termService.GetTermStatisticsAsync(projectID);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim istatistikleri alınırken hata oluştu. Proje: {ProjectID}", projectID);
                return StatusCode(500, new { message = "İstatistikler alınırken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Benzer terimleri bulur (embedding tabanlı)
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="embedding">Referans embedding vektörü</param>
        /// <param name="limit">Maksimum sonuç sayısı</param>
        /// <returns>Benzer terimler</returns>
        [HttpPost("{projectID}/similar")]
        public async Task<ActionResult<List<Term>>> FindSimilarTerms(long projectID, [FromBody] float[] embedding, [FromQuery] int limit = 10)
        {
            try
            {
                if (embedding == null || embedding.Length == 0)
                {
                    return BadRequest(new { message = "Embedding vektörü boş olamaz" });
                }

                var terms = await _termService.FindSimilarTermsAsync(embedding, projectID, limit);
                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Benzer terim arama işleminde hata oluştu. Proje: {ProjectID}", projectID);
                return StatusCode(500, new { message = "Benzer terim arama işleminde bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Yeni terim oluşturur
        /// </summary>
        /// <param name="projectID">projectID</param>
        /// <param name="termDto">Oluşturulacak terim bilgileri</param>
        /// <returns>Oluşturulan terim</returns>
        [HttpPost("{projectID}")]
        public async Task<ActionResult<Term>> CreateTerm(long projectID, [FromBody] TermCreateDto termDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var term = await _termService.CreateTermAsync(termDto, projectID);
                return CreatedAtAction(nameof(GetTermById), new { projectID, id = term.Id }, term);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim oluşturulurken hata oluştu. En: {En}, Proje: {ProjectID}", termDto.En, projectID);
                return StatusCode(500, new { message = "Terim oluşturulurken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Çoklu terim oluşturur
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="termDtos">Oluşturulacak terim listesi</param>
        /// <returns>Oluşturulan terimler</returns>
        [HttpPost("{projectID}/bulk")]
        public async Task<ActionResult<List<Term>>> CreateMultipleTerms(long projectID, [FromBody] List<TermCreateDto> termDtos)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (termDtos == null || !termDtos.Any())
                {
                    return BadRequest(new { message = "Terim listesi boş olamaz" });
                }

                var terms = await _termService.CreateTermsAsync(termDtos, projectID);
                return Ok(new { message = "Terimler başarıyla oluşturuldu", terms });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim oluşturulurken hata oluştu. Sayı: {Count}, Proje: {ProjectID}", termDtos?.Count ?? 0, projectID);
                return StatusCode(500, new { message = "Terimler oluşturulurken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Terimi günceller
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Güncellenecek terim ID'si</param>
        /// <param name="termDto">Güncelleme bilgileri</param>
        /// <returns>Güncellenmiş terim</returns>
        [HttpPut("{projectID}/{id}")]
        public async Task<ActionResult<Term>> UpdateTerm(long projectID, long id, [FromBody] TermUpdateDto termDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var term = await _termService.UpdateTermAsync(id, termDto, projectID);
                if (term == null)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı" });
                }

                return Ok(term);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim güncellenirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim güncellenirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Terimi siler
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Silinecek terim ID'si</param>
        /// <returns>Silme işlemi sonucu</returns>
        [HttpDelete("{projectID}/{id}")]
        public async Task<ActionResult> DeleteTerm(long projectID, long id)
        {
            try
            {
                var success = await _termService.DeleteTermAsync(id, projectID);
                if (!success)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı veya silinemedi" });
                }

                return Ok(new { message = "Terim başarıyla silindi" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim silinirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim silinirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Çoklu terim siler
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="ids">Silinecek terim ID'leri</param>
        /// <returns>Silme işlemi sonucu</returns>
        [HttpDelete("{projectID}/bulk")]
        public async Task<ActionResult> DeleteMultipleTerms(long projectID, [FromBody] List<long> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    return BadRequest(new { message = "ID listesi boş olamaz" });
                }

                var deletedCount = await _termService.DeleteTermsAsync(ids, projectID);
                return Ok(new { message = $"{deletedCount}/{ids.Count} terim başarıyla silindi", deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim silinirken hata oluştu. Sayı: {Count}, Proje: {ProjectID}", ids?.Count ?? 0, projectID);
                return StatusCode(500, new { message = "Terimler silinirken bir hata oluştu", error = ex.Message });
            }
        }
    }
}