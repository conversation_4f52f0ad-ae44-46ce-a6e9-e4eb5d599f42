<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Catalyst" Version="1.0.54164" />
    <PackageReference Include="Catalyst.Models.English" Version="1.0.30952" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.70.0.3834" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Microsoft.Net.Compilers.Toolset" Version="4.14.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
    <PackageReference Include="Supabase" Version="1.1.1" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.70.0.3819" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Mscc.GenerativeAI" Version="2.6.6" />
  </ItemGroup>

</Project>
