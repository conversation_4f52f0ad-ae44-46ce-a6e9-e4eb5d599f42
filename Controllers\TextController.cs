using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Text verileri için API controller
/// Metin verilerinin CRUD işlemlerini ve özel sorguları yönetir
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TextController : ControllerBase
{
    private readonly ITextService _textService;
    private readonly ILogger<TextController> _logger;

    public TextController(ITextService textService, ILogger<TextController> logger)
    {
        _textService = textService;
        _logger = logger;
    }

    /// <summary>
    /// Belirtilen şemadaki tüm metinleri getirir (filtreleme seçenekleri ile)
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="page"><PERSON><PERSON> numarası (1'den başlar)</param>
    /// <param name="pageSize"><PERSON><PERSON> boyutu</param>
    /// <param name="namespaceFilter">Namespace filtresi (contains)</param>
    /// <param name="keyFilter">Key filtresi (contains)</param>
    /// <param name="enFilter">İngilizce metin filtresi (contains)</param>
    /// <param name="trFilter">Türkçe metin filtresi (contains)</param>
    /// <param name="statusFilter">Status filtresi (exact match)</param>
    /// <returns>Metin listesi</returns>
    [HttpGet("{ProjectId}")]
    public async Task<ActionResult<List<Text>>> GetAllTexts(long ProjectId,
        [FromQuery] int page = 1, [FromQuery] int pageSize = 50,
        [FromQuery] string? namespaceFilter = null, [FromQuery] string? keyFilter = null,
        [FromQuery] string? enFilter = null, [FromQuery] string? trFilter = null,
        [FromQuery] TextStatus? statusFilter = null)
    {
        try
        {
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 1000) pageSize = 50;

            var texts = await _textService.GetAllTextsAsync(ProjectId, page, pageSize,
                namespaceFilter, keyFilter, enFilter, trFilter, statusFilter);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metinler getirilirken hata oluştu. Şema: {ProjectId}, Sayfa: {Page}, Boyut: {PageSize}, Filtreler: Namespace={Namespace}, Key={Key}, En={En}, Tr={Tr}, Status={Status}",
                ProjectId, page, pageSize, namespaceFilter, keyFilter, enFilter, trFilter, statusFilter);
            return StatusCode(500, "Metinler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// ID'ye göre metin getirir
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="id">Metin ID</param>
    /// <returns>Metin</returns>
    [HttpGet("{ProjectId}/{id}")]
    public async Task<ActionResult<Text>> GetTextById(long ProjectId, long id)
    {
        try
        {
            var text = await _textService.GetTextByIdAsync(id, ProjectId);
            if (text == null)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }
            return Ok(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin getirilirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Anahtar kelimeye göre metin arar
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="page">Sayfa numarası (1'den başlar)</param>
    /// <param name="pageSize">Sayfa boyutu</param>
    /// <returns>Metin listesi</returns>
    [HttpGet("{ProjectId}/search")]
    public async Task<ActionResult<List<Text>>> SearchTexts(long ProjectId, [FromQuery] string searchTerm, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Arama terimi boş olamaz.");
            }

            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 1000) pageSize = 50;

            var texts = await _textService.SearchTextsAsync(searchTerm, ProjectId, page, pageSize);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin arama işleminde hata oluştu. SearchTerm: {SearchTerm}, Şema: {ProjectId}, Sayfa: {Page}, Boyut: {PageSize}", searchTerm, ProjectId, page, pageSize);
            return StatusCode(500, "Metin arama işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Embedding vektörüne göre benzer metinleri bulur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <returns>Benzer metin listesi</returns>
    [HttpPost("{ProjectId}/similar")]
    public async Task<ActionResult<List<Text>>> FindSimilarTexts(long ProjectId, [FromBody] float[] embedding, [FromQuery] int limit = 10)
    {
        try
        {
            if (embedding == null || embedding.Length == 0)
            {
                return BadRequest("Embedding vektörü boş olamaz.");
            }

            var texts = await _textService.FindSimilarTextsAsync(embedding, limit, ProjectId);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer metin arama işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Benzer metin arama işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Yeni metin oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="textCreateDto">Metin oluşturma DTO</param>
    /// <returns>Oluşturulan metin</returns>
    [HttpPost("{ProjectId}")]
    public async Task<ActionResult<Text>> CreateText(long ProjectId, [FromBody] TextCreateDto textCreateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var text = await _textService.CreateTextAsync(textCreateDto, ProjectId);
            return CreatedAtAction(nameof(GetTextById), new { ProjectId, id = text.Id }, text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin oluşturulurken hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Metin oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metni günceller
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="id">Metin ID</param>
    /// <param name="textUpdateDto">Metin güncelleme DTO</param>
    /// <returns>Güncellenmiş metin</returns>
    [HttpPut("{ProjectId}/{id}")]
    public async Task<ActionResult<Text>> UpdateText(long ProjectId, long id, [FromBody] TextUpdateDto textUpdateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var text = await _textService.UpdateTextAsync(id, textUpdateDto, ProjectId);
            if (text == null)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }

            return Ok(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin güncellenirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin güncellenirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metni siler
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="id">Metin ID</param>
    /// <returns>Silme sonucu</returns>
    [HttpDelete("{ProjectId}/{id}")]
    public async Task<ActionResult> DeleteText(long ProjectId, long id)
    {
        try
        {
            var result = await _textService.DeleteTextAsync(id, ProjectId);
            if (!result)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin silinirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin silinirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Toplu metin oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="textCreateDtos">Metin oluşturma DTO listesi</param>
    /// <returns>Oluşturulan metin listesi</returns>
    [HttpPost("{ProjectId}/bulk")]
    public async Task<ActionResult<List<Text>>> CreateTexts(long ProjectId, [FromBody] List<TextCreateDto> textCreateDtos)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var texts = await _textService.CreateTextsAsync(textCreateDtos, ProjectId);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin oluşturma işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin oluşturma işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Toplu metin günceller
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="textUpdates">Metin güncelleme listesi</param>
    /// <returns>Güncellenmiş metin listesi</returns>
    [HttpPut("{ProjectId}/bulk")]
    public async Task<ActionResult<List<Text>>> UpdateTexts(long ProjectId, [FromBody] List<TextUpdateRequest> textUpdates)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var updates = textUpdates.Select(u => (u.Id, u.UpdateDto)).ToList();
            var texts = await _textService.UpdateTextsAsync(updates, ProjectId);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin güncelleme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin güncelleme işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Toplu metin siler
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="ids">Silinecek metin ID listesi</param>
    /// <returns>Silme sonucu</returns>
    [HttpDelete("{ProjectId}/bulk")]
    public async Task<ActionResult> DeleteTexts(long ProjectId, [FromBody] List<long> ids)
    {
        try
        {
            var result = await _textService.DeleteTextsAsync(ids, ProjectId);
            if (!result)
            {
                return BadRequest("Metinler silinirken bir hata oluştu.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin silme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin silme işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metin istatistiklerini getirir
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Metin istatistikleri</returns>
    [HttpGet("{ProjectId}/statistics")]
    public async Task<ActionResult<TextStatistics>> GetTextStatistics(long ProjectId)
    {
        try
        {
            var statistics = await _textService.GetTextStatisticsAsync(ProjectId);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin istatistikleri getirilirken hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Metin istatistikleri getirilirken bir hata oluştu.");
        }
    }
}

/// <summary>
/// Toplu güncelleme için yardımcı sınıf
/// </summary>
public class TextUpdateRequest
{
    public long Id { get; set; }
    public TextUpdateDto UpdateDto { get; set; } = null!;
}