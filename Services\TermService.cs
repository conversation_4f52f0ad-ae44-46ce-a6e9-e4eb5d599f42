using Microsoft.Extensions.Logging;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;


namespace TranslationAgentServer.Services
{
    /// <summary>
    /// Term servisi implementasyonu
    /// Terim verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
    /// </summary>
    public class TermService : ITermService
    {
        private readonly ISupabaseService _supabaseService;
        private readonly ILogger<TermService> _logger;
        private readonly INlpService _nlpService;
        private readonly EmbeddingHelper _embeddingHelper;

        public TermService(ISupabaseService supabaseService, ILogger<TermService> logger, INlpService nlpService, EmbeddingHelper embeddingHelper)
        {
            _supabaseService = supabaseService;
            _logger = logger;
            _embeddingHelper = embeddingHelper;
            _nlpService = nlpService;
        }

        public async Task<List<Term>> GetAllTermsAsync(long ProjectId, int page = 1, int pageSize = 50,
            string? enFilter = null, string? trFilter = null, string? categoryFilter = null, string? infoFilter = null,
             TermStatus? statusFilter = null)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var offset = (page - 1) * pageSize;

                // Önce tüm verileri al
                var query = client
                    .From<Term>().Where(c => c != null);

                // Filtreleri uygula


                if (!string.IsNullOrWhiteSpace(enFilter))
                {
                    query = query.Where(x => x.En != null && x.En.Contains(enFilter, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(trFilter))
                {
                    query = query.Where(x => x.Tr != null && x.Tr.Contains(trFilter, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(categoryFilter))
                {
                    query = query.Where(x => x.Category != null && x.Category.Contains(categoryFilter, StringComparison.OrdinalIgnoreCase));
                }
                if (!string.IsNullOrWhiteSpace(infoFilter))
                {
                    query = query.Where(x => x.Info != null && x.Info.Contains(infoFilter, StringComparison.OrdinalIgnoreCase));
                }

                if (statusFilter.HasValue)
                {
                    query = query.Where(x => x.Status == statusFilter.Value);
                }

                var result = await query
                              .Range(offset, offset + pageSize - 1)
                              .Get();
                return result.Models ?? new List<Term>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terimler getirilirken hata oluştu. Şema: {ProjectId}, Sayfa: {Page}, Boyut: {PageSize}, Filtreler: En={En}, Tr={Tr}, Status={Status}",
                    ProjectId, page, pageSize, enFilter, trFilter, statusFilter);
                throw;
            }
        }

        public async Task<Term?> GetTermByIdAsync(long id, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.Id == id)
                    .Single();

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim getirilirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
                return null;
            }
        }

        public async Task<List<Term>> SearchTermsAsync(string searchTerm, long ProjectId, int page = 1, int pageSize = 50)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.En!.Contains(searchTerm) || x.Tr!.Contains(searchTerm) || x.Lemma!.Contains(searchTerm))
                    .Range((page - 1) * pageSize, page * pageSize - 1)
                    .Get();

                return response.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim arama işleminde hata oluştu. SearchTerm: {SearchTerm}, Şema: {ProjectId}", searchTerm, ProjectId);
                throw;
            }
        }

        public async Task<List<Term>> FullTextSearchAsync(string query, long ProjectId, int page = 1, int pageSize = 50)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.En!.Contains(query) || x.Tr!.Contains(query) || x.Lemma!.Contains(query))
                    .Range((page - 1) * pageSize, page * pageSize - 1)
                    .Get();

                return response.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Full-text arama işleminde hata oluştu. Query: {Query}, Şema: {ProjectId}", query, ProjectId);
                throw;
            }
        }

        public async Task<List<Term>> FindByEnglishTermAsync(string englishTerm, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.En == englishTerm)
                    .Get();

                return response.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "İngilizce terime göre arama işleminde hata oluştu. EnglishTerm: {EnglishTerm}, Şema: {ProjectId}", englishTerm, ProjectId);
                throw;
            }
        }

        public async Task<List<Term>> FindByTurkishTermAsync(string turkishTerm, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.Tr == turkishTerm)
                    .Get();

                return response.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Türkçe terime göre arama işleminde hata oluştu. TurkishTerm: {TurkishTerm}, Şema: {ProjectId}", turkishTerm, ProjectId);
                throw;
            }
        }

        public async Task<List<Term>> FindByLemmaAsync(string lemma, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.Lemma == lemma)
                    .Get();

                return response.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lemma'ya göre arama işleminde hata oluştu. Lemma: {Lemma}, Şema: {ProjectId}", lemma, ProjectId);
                throw;
            }
        }

        public async Task<TermStatistics> GetTermStatisticsAsync(long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);

                var allTerms = await client.From<Term>().Select("*").Get();
                var terms = allTerms.Models;

                var statistics = new TermStatistics
                {
                    TotalTerms = terms.Count,
                    TranslatedTerms = terms.Count(t => !string.IsNullOrEmpty(t.Tr)),
                    UntranslatedTerms = terms.Count(t => string.IsNullOrEmpty(t.Tr)),
                    TermsWithEmbedding = terms.Count(t => t.Embedding != null && t.Embedding is float[] embArray && embArray.Length > 0),
                    TermsWithoutEmbedding = terms.Count(t => t.Embedding == null || !(t.Embedding is float[] embArray2) || embArray2.Length == 0),
                    EnglishTerms = terms.Count(t => t.Status == TermStatus.EN),
                    TurkishTerms = terms.Count(t => t.Status == TermStatus.TR)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim istatistikleri alınırken hata oluştu. Şema: {ProjectId}", ProjectId);
                throw;
            }
        }

        public async Task<List<Term>> FindSimilarTermsAsync(float[] embedding, long ProjectId, int limit = 10)
        {
            try
            {
                // Basit implementasyon - tüm terimleri al ve benzerlik hesapla
                var allTerms = await GetAllTermsAsync(ProjectId, 1, 1000);

                var termsWithEmbedding = allTerms
                    .Where(t => t.Embedding != null && t.Embedding is float[] embArray && embArray.Length > 0)
                    .Select(t => new { Term = t, Similarity = CalculateCosineSimilarity(embedding, (float[])t.Embedding!) })
                    .OrderByDescending(x => x.Similarity)
                    .Take(limit)
                    .Select(x => x.Term)
                    .ToList();

                return termsWithEmbedding;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Benzer terimler aranırken hata oluştu. Şema: {ProjectId}", ProjectId);
                throw;
            }
        }

        public async Task<Term> CreateTermAsync(TermCreateDto termCreateDto, long ProjectId)
        {
            try
            {
                var term = new Term
                {
                    RowId = termCreateDto.RowId,
                    En = termCreateDto.En,
                    Tr = termCreateDto.Tr,
                    Category = termCreateDto.Category,
                    Info = termCreateDto.Info,
                    Lemma = termCreateDto.Lemma,
                    Status = termCreateDto.Status,
                    Embedding = termCreateDto.Embedding,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Insert(term);

                return response.Model!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim oluşturulurken hata oluştu. Şema: {ProjectId}", ProjectId);
                throw;
            }
        }

        public async Task<bool> CreateTermsAsync(List<TermCreateDto> termCreateDtos, long ProjectId)
        {
            try
            {
                var terms = termCreateDtos.Select(dto => new Term
                {
                    RowId = dto.RowId,
                    En = dto.En,
                    Tr = dto.Tr,
                    Category = dto.Category,
                    Info = dto.Info,
                    Lemma = string.IsNullOrEmpty(dto.Lemma) && !string.IsNullOrEmpty(dto.En)
     ? Task.Run(async () => await _nlpService.ProcessTextToLemmaAsync(dto.En)).Result
     : dto.Lemma,
                    Status = dto.Status,
                    Embedding = dto.Embedding,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }).ToList();

                //Batch embedding işlemi (50'lük gruplar)
                var batchSize = 50;
                for (int i = 0; i < terms.Count; i += batchSize)
                {
                    var batch = terms.Skip(i).Take(batchSize).ToList();
                    var embeddings = await _embeddingHelper.GenerateEmbeddingsAsync(batch.Select(t => t.En!).ToList());
                    for (int j = 0; j < batch.Count; j++)
                    {
                        batch[j].Embedding = embeddings[j];
                    }
                }

                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Insert(terms);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim oluşturulurken hata oluştu. Şema: {ProjectId}", ProjectId);

                return false;
            }
        }

        public async Task<Term?> UpdateTermAsync(long id, TermUpdateDto termUpdateDto, long ProjectId)
        {
            try
            {
                var existingTerm = await GetTermByIdAsync(id, ProjectId);
                if (existingTerm == null)
                {
                    return null;
                }

                if (termUpdateDto.RowId.HasValue) existingTerm.RowId = termUpdateDto.RowId.Value;
                if (termUpdateDto.En != null) existingTerm.En = termUpdateDto.En;
                if (termUpdateDto.Tr != null) existingTerm.Tr = termUpdateDto.Tr;
                if (termUpdateDto.Category != null) existingTerm.Category = termUpdateDto.Category;
                if (termUpdateDto.Info != null) existingTerm.Info = termUpdateDto.Info;
                if (termUpdateDto.Lemma != null) existingTerm.Lemma = termUpdateDto.Lemma;
                if (termUpdateDto.Status.HasValue) existingTerm.Status = termUpdateDto.Status.Value;
                if (termUpdateDto.Embedding != null) existingTerm.Embedding = termUpdateDto.Embedding;

                existingTerm.UpdatedAt = DateTime.UtcNow;

                var client = await _supabaseService.GetProjectClient(ProjectId);
                var response = await client
                    .From<Term>()
                    .Where(x => x.Id == id)
                    .Update(existingTerm);

                return response.Model;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim güncellenirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
                throw;
            }
        }

        public async Task<bool> DeleteTermAsync(long id, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                await client
                    .From<Term>()
                    .Where(x => x.Id == id)
                    .Delete();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim silinirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
                return false;
            }
        }

        public async Task<int> DeleteTermsAsync(List<long> ids, long ProjectId)
        {
            try
            {
                var client = await _supabaseService.GetProjectClient(ProjectId);
                var deletedCount = 0;

                foreach (var id in ids)
                {
                    await client
                        .From<Term>()
                        .Where(x => x.Id == id)
                        .Delete();
                    deletedCount++;
                }

                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim silinirken hata oluştu. Şema: {ProjectId}", ProjectId);
                throw;
            }
        }


        private static float CalculateCosineSimilarity(float[] vectorA, float[] vectorB)
        {
            if (vectorA.Length != vectorB.Length)
                return 0f;

            float dotProduct = 0f;
            float magnitudeA = 0f;
            float magnitudeB = 0f;

            for (int i = 0; i < vectorA.Length; i++)
            {
                dotProduct += vectorA[i] * vectorB[i];
                magnitudeA += vectorA[i] * vectorA[i];
                magnitudeB += vectorB[i] * vectorB[i];
            }

            magnitudeA = (float)Math.Sqrt(magnitudeA);
            magnitudeB = (float)Math.Sqrt(magnitudeB);

            if (magnitudeA == 0f || magnitudeB == 0f)
                return 0f;

            return dotProduct / (magnitudeA * magnitudeB);
        }
    }
}