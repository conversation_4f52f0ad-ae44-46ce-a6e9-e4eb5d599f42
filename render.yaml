# Render.com deployment konfigürasyonu
# Bu dosya Render.com'da otomatik deployment için gerekli ayarları içerir

services:
  - type: web
    name: translation-agent-server
    runtime: docker
    plan: free  # Ücretsiz plan, gerekirse starter veya pro olarak değiştirilebilir
    
    # Docker build konfigürasyonu
    dockerfilePath: ./Dockerfile
    dockerContext: ./
    
    # Environment variables
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:8080
    
    # Health check endpoint (opsiyonel)
    healthCheckPath: /health
    
    # Auto-deploy konfigürasyonu
    autoDeploy: true
    
    # Build command (Docker kullanıldığında gerekli değil)
    # buildCommand: dotnet publish -c Release -o /app/publish
    
    # Start command (<PERSON>er kullanıldığında gerekli değil)
    # startCommand: dotnet TranslationAgentServer.dll