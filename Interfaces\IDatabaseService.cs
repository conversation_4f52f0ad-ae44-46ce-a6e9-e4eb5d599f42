using TranslationAgentServer.Data;
using TranslationAgentServer.Models;
using System;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// PostgreSQL veritabanı servisi için arayüz
/// Entity Framework Core ile veritabanı işlemlerini yönetir
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// Contexti dönderir
    /// </summary>
    /// <returns>Context</returns>
    ApplicationDbContext GetContext();


    /// <summary>
    /// Proje için özel DbContext'i dönderir
    /// </summary>
    /// <returns>Context</returns>
    DynamicSchemaDbContext GetProjectContext(int projectId);

    /// <summary>
    /// Veritabanı bağlantısını test eder
    /// </summary>
    /// <returns>Bağlantı durumu</returns>
    Task<bool> TestConnectionAsync();

    /// <summary>
    /// Veritabanını başlatır ve migration'ları uygular
    /// </summary>
    /// <returns>Başlatma sonucu</returns>
    Task<bool> InitializeDatabaseAsync();

    /// <summary>
    /// Pending migration'ları uygular
    /// </summary>
    /// <returns>Uygulama sonucu</returns>
    Task<bool> ApplyMigrationsAsync();

    /// <summary>
    /// Veritabanı şemasını oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturma sonucu</returns>
    Task<bool> CreateSchemaAsync(string schemaName);

    /// <summary>
    /// Şema var mı kontrol eder
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Şema varlık durumu</returns>
    Task<bool> SchemaExistsAsync(string schemaName);

    /// <summary>
    /// Tablo var mı kontrol eder
    /// </summary>
    /// <param name="tableName">Tablo adı</param>
    /// <param name="schemaName">Şema adı (opsiyonel)</param>
    /// <returns>Tablo varlık durumu</returns>
    Task<bool> TableExistsAsync(string tableName, string? schemaName = null);

    /// <summary>
    /// Proje için özel şema ve tabloları oluşturur
    /// </summary>
    /// <param name="projectId">Proje ID'si</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturma sonucu</returns>
    Task<CreateSchemaResult> CreateProjectSchemaAsync(int projectId, string schemaName);

    /// <summary>
    /// Şemayı siler
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Silme sonucu</returns>
    Task<bool> DropSchemaAsync(string schemaName);

    /// <summary>
    /// Veritabanı istatistiklerini getirir
    /// </summary>
    /// <returns>İstatistik bilgileri</returns>
    Task<Dictionary<string, object>> GetDatabaseStatisticsAsync();

    /// <summary>
    /// Raw SQL sorgusu çalıştırır
    /// </summary>
    /// <param name="sql">SQL sorgusu</param>
    /// <param name="parameters">Parametreler</param>
    /// <returns>Sorgu sonucu</returns>
    Task<List<Dictionary<string, object>>> ExecuteRawSqlAsync(string sql, params object[] parameters);

    /// <summary>
    /// Veritabanı backup'ı alır
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Backup sonucu</returns>
    Task<bool> CreateBackupAsync(string backupPath);

    /// <summary>
    /// Backup'tan veritabanını geri yükler
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Geri yükleme sonucu</returns>
    Task<bool> RestoreBackupAsync(string backupPath);

    /// <summary>
    /// Belirtilen şema için dinamik DbContext oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Dinamik şema DbContext'i</returns>
    DynamicSchemaDbContext CreateDynamicContext(string schemaName);

}