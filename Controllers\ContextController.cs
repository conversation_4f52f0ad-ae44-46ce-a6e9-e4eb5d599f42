using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Context yönetimi için API controller'ı
/// Context verilerinin CRUD işlemlerini ve özel sorguları sağlar
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ContextController : ControllerBase
{
    private readonly IContextService _contextService;
    private readonly ILogger<ContextController> _logger;

    public ContextController(IContextService contextService, ILogger<ContextController> logger)
    {
        _contextService = contextService;
        _logger = logger;
    }

    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    /// <param name="projectId"><PERSON><PERSON> k<PERSON></param>
    /// <param name="page"><PERSON><PERSON> numarası (varsayılan: 1)</param>
    /// <param name="pageSize"><PERSON><PERSON> boyutu (varsayılan: 50, maksimum: 100)</param>
    /// <param name="categoryFilter">Kategori filtresi</param>
    /// <param name="titleFilter">Başlık filtresi</param>
    /// <param name="contentFilter">İçerik filtresi</param>
    /// <returns>Context listesi</returns>
    [HttpGet("{projectId}")]
    public async Task<ActionResult<List<Context>>> GetAllContexts(
        long projectId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? categoryFilter = null,
        [FromQuery] string? titleFilter = null,
        [FromQuery] string? contentFilter = null)
    {
        try
        {
            // Parametre doğrulama
            if (page < 1)
            {
                return BadRequest("Sayfa numarası 1'den küçük olamaz.");
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest("Sayfa boyutu 1-100 arasında olmalıdır.");
            }

            var contexts = await _contextService.GetAllContextsAsync(projectId, page, pageSize,
                categoryFilter, titleFilter, contentFilter);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler getirilirken hata oluştu");
            return StatusCode(500, "Context'ler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <returns>Context</returns>
    [HttpGet("{projectId}/{id}")]
    public async Task<ActionResult<Context>> GetContextById(
        long projectId,
        long id)
    {
        try
        {
            var context = await _contextService.GetContextByIdAsync(id, projectId);

            if (context == null)
            {
                return NotFound($"ID'si {id} olan context bulunamadı.");
            }

            return Ok(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context getirilirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Anahtar kelimeye göre context arar
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="page">Sayfa numarası (varsayılan: 1)</param>
    /// <param name="pageSize">Sayfa boyutu (varsayılan: 50, maksimum: 100)</param>
    /// <returns>Context listesi</returns>
    [HttpGet("{projectId}/search")]
    public async Task<ActionResult<List<Context>>> SearchContexts(
        long projectId,
        [FromQuery] string searchTerm,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            // Parametre doğrulama
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Arama terimi boş olamaz.");
            }

            if (page < 1)
            {
                return BadRequest("Sayfa numarası 1'den küçük olamaz.");
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest("Sayfa boyutu 1-100 arasında olmalıdır.");
            }

            var contexts = await _contextService.SearchContextsAsync(searchTerm, projectId, page, pageSize);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context arama işleminde hata oluştu. SearchTerm: {SearchTerm}", searchTerm);
            return StatusCode(500, "Context arama işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Full-text search kullanarak context arar
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="page">Sayfa numarası (varsayılan: 1)</param>
    /// <param name="pageSize">Sayfa boyutu (varsayılan: 50, maksimum: 100)</param>
    /// <returns>Context listesi</returns>
    [HttpGet("{projectId}/fulltext-search")]
    public async Task<ActionResult<List<Context>>> FullTextSearchContexts(
        long projectId,
        [FromQuery] string searchTerm,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            // Parametre doğrulama
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Arama terimi boş olamaz.");
            }

            if (page < 1)
            {
                return BadRequest("Sayfa numarası 1'den küçük olamaz.");
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest("Sayfa boyutu 1-100 arasında olmalıdır.");
            }

            var contexts = await _contextService.FullTextSearchContextsAsync(searchTerm, projectId, page, pageSize);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full-text search işleminde hata oluştu. SearchTerm: {SearchTerm}", searchTerm);
            return StatusCode(500, "Full-text search işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti (varsayılan: 10, maksimum: 50)</param>
    /// <returns>Benzer context listesi</returns>
    [HttpPost("{projectId}/similar")]
    public async Task<ActionResult<List<Context>>> FindSimilarContexts(
        long projectId,
        [FromBody] float[] embedding,
        [FromQuery] int limit = 10)
    {
        try
        {
            // Parametre doğrulama
            if (embedding == null || embedding.Length == 0)
            {
                return BadRequest("Embedding vektörü boş olamaz.");
            }

            if (limit < 1 || limit > 50)
            {
                return BadRequest("Limit 1-50 arasında olmalıdır.");
            }

            var contexts = await _contextService.FindSimilarContextsAsync(embedding, limit, projectId);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer context'ler bulunurken hata oluştu");
            return StatusCode(500, "Benzer context'ler bulunurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="contextCreateDto">Context oluşturma DTO'su</param>
    /// <returns>Oluşturulan context</returns>
    [HttpPost("{projectId}")]
    public async Task<ActionResult<Context>> CreateContext(
        long projectId,
        [FromBody] ContextCreateDto contextCreateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var context = await _contextService.CreateContextAsync(contextCreateDto, projectId);
            return CreatedAtAction(nameof(GetContextById), new { projectId, id = context.Id }, context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context oluşturulurken hata oluştu");
            return StatusCode(500, "Context oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context günceller
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <param name="contextUpdateDto">Context güncelleme DTO'su</param>
    /// <returns>Güncellenmiş context</returns>
    [HttpPut("{projectId}/{id}")]
    public async Task<ActionResult<Context>> UpdateContext(
        long projectId,
        long id,
        [FromBody] ContextUpdateDto contextUpdateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var context = await _contextService.UpdateContextAsync(id, contextUpdateDto, projectId);

            if (context == null)
            {
                return NotFound($"ID'si {id} olan context bulunamadı.");
            }

            return Ok(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context güncellenirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context güncellenirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context siler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <returns>Silme işlemi sonucu</returns>
    [HttpDelete("{projectId}/{id}")]
    public async Task<ActionResult> DeleteContext(
        long projectId,
        long id)
    {
        try
        {
            var result = await _contextService.DeleteContextAsync(id, projectId);

            if (!result)
            {
                return NotFound($"ID'si {id} olan context bulunamadı veya silinemedi.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context silinirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context silinirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="contextCreateDtos">Context oluşturma DTO listesi</param>
    /// <returns>Oluşturulan context listesi</returns>
    [HttpPost("{projectId}/bulk")]
    public async Task<ActionResult<List<Context>>> CreateContexts(
        long projectId,
        [FromBody] List<ContextCreateDto> contextCreateDtos)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (contextCreateDtos == null || contextCreateDtos.Count == 0)
            {
                return BadRequest("Context listesi boş olamaz.");
            }

            if (contextCreateDtos.Count > 100)
            {
                return BadRequest("Tek seferde en fazla 100 context oluşturulabilir.");
            }

            var contexts = await _contextService.CreateContextsAsync(contextCreateDtos, projectId);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak oluşturulurken hata oluştu");
            return StatusCode(500, "Context'ler oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="ids">Silinecek context ID'leri</param>
    /// <returns>Silme işlemi sonucu</returns>
    [HttpDelete("{projectId}/bulk")]
    public async Task<ActionResult> DeleteContexts(
        long projectId,
        [FromBody] List<long> ids)
    {
        try
        {
            if (ids == null || ids.Count == 0)
            {
                return BadRequest("ID listesi boş olamaz.");
            }

            if (ids.Count > 100)
            {
                return BadRequest("Tek seferde en fazla 100 context silinebilir.");
            }

            var result = await _contextService.DeleteContextsAsync(ids, projectId);

            if (!result)
            {
                return BadRequest("Context'ler silinemedi.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak silinirken hata oluştu");
            return StatusCode(500, "Context'ler silinirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context istatistikleri</returns>
    [HttpGet("{projectId}/statistics")]
    public async Task<ActionResult<ContextStatistics>> GetContextStatistics(
        long projectId)
    {
        try
        {
            var statistics = await _contextService.GetContextStatisticsAsync(projectId);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context istatistikleri getirilirken hata oluştu");
            return StatusCode(500, "Context istatistikleri getirilirken bir hata oluştu.");
        }
    }

}