using System.Text.Json;
using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Helpers;

/// <summary>
/// Embedding oluşturma işlemleri için yardımcı sınıf
/// Supabase edge function kullanarak metin embedding'leri o<PERSON>
/// </summary>
public class EmbeddingHelper
{
    private readonly ISupabaseService _supabaseService;
    private readonly ILogger<EmbeddingHelper> _logger;

    /// <summary>
    /// EmbeddingHelper constructor
    /// </summary>
    /// <param name="supabaseService">Supabase servisi</param>
    /// <param name="logger">Logger servisi</param>
    public EmbeddingHelper(ISupabaseService supabaseService, ILogger<EmbeddingHelper> logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Verilen metin için embedding oluşturur.
    /// </summary>
    /// <param name="text">Embedding oluşturulacak metin.</param>
    /// <returns>Metnin embedding'i olarak float dizisi veya hata durumunda null.</returns>
    public async Task<float[]?> GenerateEmbeddingAsync(string text)
    {
        _logger.LogInformation("Embedding oluşturuluyor: {Text}", text);

        try
        {
            var response = await _supabaseService.InvokeEdgeFunctionAsync<JsonElement>("small-embedding", new Supabase.Functions.Client.InvokeFunctionOptions
            {
                Body = new Dictionary<string, object>
                {
                    { "text", new List<string> { text } }
                }
            });

            // Supabase fonksiyonundan dönen JSON sonucunu ayrıştır ve logla
            if (response.TryGetProperty("embeddings", out var embeddingsElement) && embeddingsElement.ValueKind == JsonValueKind.Array)
            {
                // İlk embedding dizisini al
                var firstEmbeddingArray = embeddingsElement.EnumerateArray().FirstOrDefault();

                if (firstEmbeddingArray.ValueKind == JsonValueKind.Array)
                {
                    // float dizisine dönüştür
                    var embeddings = firstEmbeddingArray.EnumerateArray().Select(e => (float)e.GetDouble()).ToArray();
                    return embeddings;
                }
                else
                {
                    _logger.LogError("Embedding dizisi oluşturulamadı. Yanıt: {Response}", response.ToString());
                }
            }
            else
            {
                _logger.LogError("Embedding oluşturulamadı. Yanıt: {Response}", response.ToString());
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Embedding oluşturulurken hata oluştu. Text: {Text}", text);
            return null;
        }
    }

    /// <summary>
    /// Verilen metinler için embedding oluşturur.
    /// </summary>
    /// <param name="texts">Embedding oluşturulacak metinler.</param>
    /// <returns>Metinlerin embedding'leri olarak float dizisi listesi veya hata durumunda null.</returns>
    public async Task<List<float[]>?> GenerateEmbeddingsAsync(List<string> texts)
    {
        _logger.LogInformation("Embedding oluşturuluyor: Text sayısı {Texts}", texts.Count);

        try
        {
            var response = await _supabaseService.InvokeEdgeFunctionAsync<JsonElement>("small-embedding", new Supabase.Functions.Client.InvokeFunctionOptions
            {
                Body = new Dictionary<string, object>
                {
                    { "texts", texts }
                }
            });

            // Supabase fonksiyonundan dönen JSON sonucunu ayrıştır ve logla
            if (response.TryGetProperty("embeddings", out var embeddingsElement) && embeddingsElement.ValueKind == JsonValueKind.Array)
            {
                // Her bir embedding dizisini al ve float dizisine dönüştür
                var embeddings = embeddingsElement.EnumerateArray().Select(e => e.EnumerateArray().Select(v => (float)v.GetDouble()).ToArray()).ToList();
                return embeddings;
            }
            else
            {
                _logger.LogError("Embedding oluşturulamadı. Yanıt: {Response}", response.ToString());
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Embedding oluşturulurken hata oluştu. Texts: {Texts}", texts);
            return null;
        }
    }

    /// <summary>
    /// İki vektör arasındaki cosine benzerliğini hesaplar
    /// </summary>
    /// <param name="vectorA">İlk vektör</param>
    /// <param name="vectorB">İkinci vektör</param>
    /// <returns>Cosine benzerlik değeri (0-1 arası)</returns>
    public static double CalculateCosineSimilarity(float[] vectorA, float[] vectorB)
    {
        if (vectorA.Length != vectorB.Length)
            return 0;

        double dotProduct = 0;
        double magnitudeA = 0;
        double magnitudeB = 0;

        for (int i = 0; i < vectorA.Length; i++)
        {
            dotProduct += vectorA[i] * vectorB[i];
            magnitudeA += vectorA[i] * vectorA[i];
            magnitudeB += vectorB[i] * vectorB[i];
        }

        magnitudeA = Math.Sqrt(magnitudeA);
        magnitudeB = Math.Sqrt(magnitudeB);

        if (magnitudeA == 0 || magnitudeB == 0)
            return 0;

        return dotProduct / (magnitudeA * magnitudeB);
    }
}