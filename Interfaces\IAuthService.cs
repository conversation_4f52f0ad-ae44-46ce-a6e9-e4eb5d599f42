using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Kimlik doğrulama işlemleri için servis arayüzü
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// Şifre ile giriş yapar
    /// </summary>
    /// <param name="password">G<PERSON>ş şifresi</param>
    /// <returns>Login sonucu</returns>
    Task<LoginResponse> LoginAsync(string password);

    /// <summary>
    /// Session'ın geçerli olup olmadığını kontrol eder
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <returns>Session geçerli mi</returns>
    Task<bool> ValidateSessionAsync(string sessionId);

    /// <summary>
    /// Session'ı sonlandırır
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <returns>Logout başar<PERSON>l<PERSON> mı</returns>
    Task<bool> LogoutAsync(string sessionId);
}