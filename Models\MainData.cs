using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;



namespace TranslationAgentServer.Models;

/// <summary>
/// Supabase main tablosu için model sınıfı
/// </summary>
[Table("main")]
public class MainData
{
    /// <summary>
    /// Benzersiz kimlik
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public long Id { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Veri ismi
    /// </summary>
    [Column("name")]
    public string? Name { get; set; }

    /// <summary>
    /// Veri değeri
    /// </summary>
    [Column("value")]
    public string? Value { get; set; }
}