using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Converters;
using Npgsql;
using TranslationAgentServer.Configurations;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Middleware;
using TranslationAgentServer.Models;
using TranslationAgentServer.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// JSON serialization'ı Newtonsoft.Json ile değiştir
builder.Services.AddControllers().AddNewtonsoftJson(opts =>
    {
        opts.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter> { new StringEnumConverter { CamelCaseText = true } };
        opts.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
    });

// Supabase yapılandırmasını ekle
builder.Services.Configure<SupabaseConfig>(builder.Configuration.GetSection("Supabase"));

// Supabase servisini DI container'a ekle
builder.Services.AddSingleton<ISupabaseService, SupabaseService>();

// PostgreSQL EF Core yapılandırması
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

// Enum'ları kaydet
var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
dataSourceBuilder.MapEnum<ProcessStatus>("ProcessStatus");
dataSourceBuilder.MapEnum<ProcessTaskType>("ProcessTaskType");
dataSourceBuilder.MapEnum<TextStatus>("TextStatus");
dataSourceBuilder.MapEnum<TermStatus>("TermStatus");

var dataSource = dataSourceBuilder.Build();

// DbContext'i kaydet
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(dataSource));


// Database Service
builder.Services.AddScoped<IDatabaseService, DatabaseService>();

// Authentication servisini DI container'a ekle
builder.Services.AddScoped<IAuthService, AuthService>();

// Project servisini DI container'a ekle
builder.Services.AddSingleton<IProjectService, ProjectService>();

// Process servisini DI container'a ekle
builder.Services.AddSingleton<IProcessService, ProcessService>();

// Google Sheets servisini DI container'a ekle
builder.Services.AddSingleton<IGoogleSheetsService, GoogleSheetsService>();

// NLP servisini DI container'a ekle
builder.Services.AddSingleton<INlpService, NlpService>();

// Gemini servisini DI container'a ekle
builder.Services.AddSingleton<IGeminiService, GeminiService>();

// Text servisini DI container'a ekle
builder.Services.AddSingleton<ITextService, TextService>();

// Term servisini DI container'a ekle
builder.Services.AddSingleton<ITermService, TermService>();

// Context servisini DI container'a ekle
builder.Services.AddSingleton<IContextService, ContextService>();

// EmbeddingHelper'ı DI container'a ekle
builder.Services.AddSingleton<EmbeddingHelper>();


builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

// Supabase servisini başlat
var supabaseService = app.Services.GetRequiredService<ISupabaseService>();
await supabaseService.InitializeAsync();

// Google Sheets servisini otomatik olarak başlat
try
{
    var googleSheetsService = app.Services.GetRequiredService<IGoogleSheetsService>();

    // Supabase'den service_account verisini çek
    var serviceAccountData = await supabaseService.GetMainValueAsync("service_account");

    if (serviceAccountData != null)
    {
        // Google Sheets servisini başlat
        var initResult = await googleSheetsService.InitializeServiceAsync(serviceAccountData);

        if (initResult.Success)
        {
            Console.WriteLine("✅ Google Sheets servisi başarıyla başlatıldı.");
        }
        else
        {
            Console.WriteLine($"❌ Google Sheets servisi başlatılamadı: {initResult.ErrorMessage}");
        }
    }
    else
    {
        Console.WriteLine("⚠️ Supabase main tablosunda 'service_account' verisi bulunamadı.");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Google Sheets servisi başlatılırken hata oluştu: {ex.Message}");
}

// NLP servisini başlat ve TextProcessingHelper'a set et
try
{
    var nlpService = app.Services.GetRequiredService<INlpService>();

    // TextProcessingHelper'a NLP servisini set et
    TextProcessingHelper.SetNlpService(nlpService);

    // NLP servisini arka planda başlat (blocking olmayan)
    _ = Task.Run(async () =>
    {
        try
        {
            var initialized = await nlpService.InitializeAsync();
            if (initialized)
            {
                Console.WriteLine("✅ NLP servisi başarıyla başlatıldı.");
            }
            else
            {
                Console.WriteLine("❌ NLP servisi başlatılamadı.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ NLP servisi başlatılırken hata oluştu: {ex.Message}");
        }
    });
}
catch (Exception ex)
{
    Console.WriteLine($"❌ NLP servisi yapılandırılırken hata oluştu: {ex.Message}");
}


// Gemini servisini başlat
try
{
    var geminiService = app.Services.GetRequiredService<IGeminiService>();


    // Supabase'den gemini_api verisini çek
    var gemini_api = await supabaseService.GetMainValueAsync("gemini_api");

    // Gemini servisini başlat
    try
    {
        var initialized = geminiService.InitializeAsync(gemini_api);
        if (initialized)
        {
            Console.WriteLine("✅ Gemini servisi başarıyla başlatıldı.");
        }
        else
        {
            Console.WriteLine("❌ Gemini servisi başlatılamadı.");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Gemini servisi başlatılırken hata oluştu: {ex.Message}");
    }

}
catch (Exception ex)
{
    Console.WriteLine($"❌ Gemini servisi yapılandırılırken hata oluştu: {ex.Message}");
}


// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// Development ortamında HTTPS redirection'ı devre dışı bırak
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Authentication middleware'ini ekle
app.UseMiddleware<AuthenticationMiddleware>();

app.UseAuthorization();

app.MapControllers();



/// <summary>
/// Ana sayfa endpoint'i - API'nin çalıştığını doğrular
/// </summary>
app.MapGet("/", () => Results.Ok(new
{
    message = "Translation Agent Server API",
    version = "1.0.0",
    status = "running",
    timestamp = DateTime.UtcNow,
    endpoints = new
    {
        health = "/health",
        swagger = "/swagger",
        auth = new
        {
            login = "/api/auth/login",
            logout = "auth/logout",
            status = "/api/auth/status"
        },
        projects = new
        {
            list = "/api/project",
            create = "/api/project",
            get = "/api/project/{id}",
            update = "/api/project/{id}",
            delete = "/api/project/{id}",
            search = "/api/project/search?name={name}",
            bySpreadsheet = "/api/project/by-spreadsheet/{spreadsheetId}",
            processes = "/api/project/{projectId}/processes"
        },
        processes = new
        {
            get = "/api/process/{id}",
            createandstart = "/api/process",
            update = "/api/process/{id}",
            delete = "/api/process/{id}",
            pause = "/api/process/{id}/pause",
            resume = "/api/process/{id}/resume",
            cancel = "/api/process/{id}/cancel",
            ping = "/api/process/{id}/ping",
        },
        googleSheets = new
        {
            initialize = "/api/googlesheets/initialize",
            status = "/api/googlesheets/status",
            spreadsheets = "/api/googlesheets/spreadsheets",
            createSpreadsheet = "/api/googlesheets/spreadsheets",
            getSpreadsheet = "/api/googlesheets/spreadsheets/{spreadsheetId}",
            sheets = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets",
            createSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets",
            deleteSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}",
            sheetData = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/data",
            rangeData = "/api/googlesheets/spreadsheets/{spreadsheetId}/range?range={range}",
            addRow = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows",
            addRows = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows/batch",
            deleteRow = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows/{rowIndex}",
            deleteRows = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows?startRowIndex={start}&endRowIndex={end}",
            updateCell = "/api/googlesheets/spreadsheets/{spreadsheetId}/cell?range={range}",
            updateRange = "/api/googlesheets/spreadsheets/{spreadsheetId}/range?range={range}",
            batchUpdate = "/api/googlesheets/spreadsheets/{spreadsheetId}/batch",
            validateHeader = "/api/googlesheets/validate-header",
            validateTextsColumns = "/api/googlesheets/validate-texts-columns/{spreadsheetId}/{sheetName}",
            validateTermsColumns = "/api/googlesheets/validate-terms-columns/{spreadsheetId}/{sheetName}",
            createTerimceSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/create-terimce-sheet"
        },
        texts = new
        {
            list = "/api/text/{schemaName}?page={page}&pageSize={pageSize}&namespaceFilter={namespace}&keyFilter={key}&enFilter={en}&trFilter={tr}&statusFilter={status}",
            get = "/api/text/{schemaName}/{id}",
            create = "/api/text/{schemaName}",
            update = "/api/text/{schemaName}/{id}",
            delete = "/api/text/{schemaName}/{id}",
            search = "/api/text/{schemaName}/search?searchTerm={term}",
            similar = "/api/text/{schemaName}/similar",
            bulk = "/api/text/{schemaName}/bulk",
            statistics = "/api/text/{schemaName}/statistics"
        },
        terms = new
        {
            list = "/api/term/{schemaName}",
            get = "/api/term/{schemaName}/{id}",
            create = "/api/term/{schemaName}",
            update = "/api/term/{schemaName}/{id}",
            delete = "/api/term/{schemaName}/{id}",
            search = "/api/term/{schemaName}/search?searchTerm={term}",
            fullTextSearch = "/api/term/{schemaName}/fulltext-search?query={query}",
            findEnglish = "/api/term/{schemaName}/find/english/{englishTerm}",
            findTurkish = "/api/term/{schemaName}/find/turkish/{turkishTerm}",
            findLemma = "/api/term/{schemaName}/find/lemma/{lemma}",
            statistics = "/api/term/{schemaName}/statistics",
            similar = "/api/term/{schemaName}/similar",
            bulk = "/api/term/{schemaName}/bulk",
        },
        contexts = new
        {
            list = "/api/context/{schemaName}",
            get = "/api/context/{schemaName}/{id}",
            create = "/api/context/{schemaName}",
            update = "/api/context/{schemaName}/{id}",
            delete = "/api/context/{schemaName}/{id}",
            search = "/api/context/{schemaName}/search?searchTerm={term}&page={page}&pageSize={pageSize}",
            fullTextSearch = "/api/context/{schemaName}/fulltext-search?searchTerm={term}&page={page}&pageSize={pageSize}",
            similar = "/api/context/{schemaName}/similar?limit={limit}",
            bulk = "/api/context/{schemaName}/bulk",
            statistics = "/api/context/{schemaName}/statistics",
        },
    }
}));



app.Run();
