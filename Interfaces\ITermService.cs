using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// Terim yönetimi için servis arayüzü.
    /// CRUD işlemleri, arama ve özel terim işlevlerini tanımlar.
    /// </summary>
    public interface ITermService
    {
        /// <summary>
        /// Tüm terimleri getirir
        /// </summary>
        /// <param name="ProjectId"><PERSON>je kim<PERSON></param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="sheetIdFilter">Sheet ID filtresi</param>
        /// <param name="enFilter">İngilizce terim filtresi</param>
        /// <param name="trFilter">Türkçe terim filtresi</param>
        /// <param name="categoryFilter">Kategori filtresi</param>
        /// <param name="infoFilter">B<PERSON>gi filtresi</param>
        /// <param name="lemmaFilter">Lemma filtresi</param>
        /// <param name="statusFilter">Durum filtresi</param>
        /// <returns>Terim listesi</returns>
        Task<List<Term>> GetAllTermsAsync(long ProjectId, int page = 1, int pageSize = 50,
            string? enFilter = null, string? trFilter = null,
            string? categoryFilter = null,
            string? infoFilter = null,
            TermStatus? statusFilter = null);

        /// <summary>
        /// ID'ye göre terim getirir
        /// </summary>
        /// <param name="id">Terim ID'si</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Bulunan terim veya null</returns>
        Task<Term?> GetTermByIdAsync(long id, long ProjectId);

        /// <summary>
        /// Terim arama yapar
        /// </summary>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Arama sonuçları</returns>
        Task<List<Term>> SearchTermsAsync(string searchTerm, long ProjectId, int page = 1, int pageSize = 50);

        /// <summary>
        /// Full-text search yapar
        /// </summary>
        /// <param name="query">Arama sorgusu</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Arama sonuçları</returns>
        Task<List<Term>> FullTextSearchAsync(string query, long ProjectId, int page = 1, int pageSize = 50);

        /// <summary>
        /// İngilizce terime göre arama yapar
        /// </summary>
        /// <param name="englishTerm">İngilizce terim</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Bulunan terimler</returns>
        Task<List<Term>> FindByEnglishTermAsync(string englishTerm, long ProjectId);

        /// <summary>
        /// Türkçe terime göre arama yapar
        /// </summary>
        /// <param name="turkishTerm">Türkçe terim</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Bulunan terimler</returns>
        Task<List<Term>> FindByTurkishTermAsync(string turkishTerm, long ProjectId);

        /// <summary>
        /// Lemma'ya göre arama yapar
        /// </summary>
        /// <param name="lemma">Lemma</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Bulunan terimler</returns>
        Task<List<Term>> FindByLemmaAsync(string lemma, long ProjectId);

        /// <summary>
        /// Terim istatistiklerini getirir
        /// </summary>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<TermStatistics> GetTermStatisticsAsync(long ProjectId);

        /// <summary>
        /// Benzer terimleri bulur (embedding tabanlı)
        /// </summary>
        /// <param name="embedding">Referans embedding vektörü</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <param name="limit">Maksimum sonuç sayısı</param>
        /// <returns>Benzer terimler</returns>
        Task<List<Term>> FindSimilarTermsAsync(float[] embedding, long ProjectId, int limit = 10);

        /// <summary>
        /// Yeni terim oluşturur
        /// </summary>
        /// <param name="termCreateDto">Oluşturulacak terim bilgileri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Oluşturulan terim</returns>
        Task<Term> CreateTermAsync(TermCreateDto termCreateDto, long ProjectId);

        /// <summary>
        /// Çoklu terim oluşturur
        /// </summary>
        /// <param name="termCreateDtos">Oluşturulacak terim listesi</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Oluşturulan terimler</returns>
        Task<bool> CreateTermsAsync(List<TermCreateDto> termCreateDtos, long ProjectId);

        /// <summary>
        /// Terimi günceller
        /// </summary>
        /// <param name="id">Güncellenecek terim ID'si</param>
        /// <param name="termUpdateDto">Güncelleme bilgileri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Güncellenmiş terim veya null</returns>
        Task<Term?> UpdateTermAsync(long id, TermUpdateDto termUpdateDto, long ProjectId);

        /// <summary>
        /// Terimi siler
        /// </summary>
        /// <param name="id">Silinecek terim ID'si</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Silme işlemi başarılı mı?</returns>
        Task<bool> DeleteTermAsync(long id, long ProjectId);

        /// <summary>
        /// Çoklu terim siler
        /// </summary>
        /// <param name="ids">Silinecek terim ID'leri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Silinen terim sayısı</returns>
        Task<int> DeleteTermsAsync(List<long> ids, long ProjectId);
    }

    /// <summary>
    /// Terim istatistikleri için model
    /// </summary>
    public class TermStatistics
    {
        /// <summary>
        /// Toplam terim sayısı
        /// </summary>
        public int TotalTerms { get; set; }

        /// <summary>
        /// Çevrilmiş terim sayısı
        /// </summary>
        public int TranslatedTerms { get; set; }

        /// <summary>
        /// Çevrilmemiş terim sayısı
        /// </summary>
        public int UntranslatedTerms { get; set; }

        /// <summary>
        /// İngilizce terim sayısı
        /// </summary>
        public int EnglishTerms { get; set; }

        /// <summary>
        /// Türkçe terim sayısı
        /// </summary>
        public int TurkishTerms { get; set; }

        /// <summary>
        /// Embedding'i olan terim sayısı
        /// </summary>
        public int TermsWithEmbedding { get; set; }

        /// <summary>
        /// Embedding'i olmayan terim sayısı
        /// </summary>
        public int TermsWithoutEmbedding { get; set; }

        /// <summary>
        /// Çeviri tamamlanma yüzdesi
        /// </summary>
        public double TranslationCompletionPercentage => TotalTerms > 0 ? (double)TranslatedTerms / TotalTerms * 100 : 0;

        /// <summary>
        /// Embedding tamamlanma yüzdesi
        /// </summary>
        public double EmbeddingCompletionPercentage => TotalTerms > 0 ? (double)TermsWithEmbedding / TotalTerms * 100 : 0;
    }
}