using System.Data;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// PostgreSQL veritabanı servisi implementasyonu
/// Entity Framework Core ile veritabanı işlemlerini yönetir
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly ApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DatabaseService> _logger;
    private readonly string _connectionString;
    private DynamicSchemaDbContext? _projectContext;


    /// <summary>
    /// DatabaseService constructor
    /// </summary>
    /// <param name="context">Veritabanı bağlamı</param>
    /// <param name="configuration">Konfigürasyon servisi</param>
    /// <param name="logger">Logger servisi</param>

    public DatabaseService(
        ApplicationDbContext context,
        IConfiguration configuration,
        ILogger<DatabaseService> logger)
    {
        _context = context;
        _configuration = configuration;
        _logger = logger;
        _connectionString = _configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("DefaultConnection connection string not found.");

    }
    /// <summary>
    /// Contexti dönderir
    /// </summary>
    /// <returns>Context</returns>
    public ApplicationDbContext GetContext()
    {
        return _context;
    }

    /// <summary>
    /// Proje için özel DbContext'i dönderir
    /// </summary>
    /// <returns>Context</returns>
    public DynamicSchemaDbContext GetProjectContext(int projectId)
    {
        var project = _context.Projects.FirstOrDefault(p => p.Id == projectId);
        if (project == null)
        {
            throw new Exception($"Proje bulunamadı: {projectId}");
        }

        var schemaName = project.SchemaName;
        if (_projectContext != null && _projectContext.SchemaName == schemaName)
        {
            return _projectContext;
        }


        _projectContext = CreateDynamicContext(schemaName);

        return _projectContext;
    }

    /// <summary>
    /// Veritabanı bağlantısını test eder
    /// </summary>
    /// <returns>Bağlantı durumu</returns>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            await _context.Database.OpenConnectionAsync();
            await _context.Database.CloseConnectionAsync();
            _logger.LogInformation("Veritabanı bağlantısı başarıyla test edildi.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veritabanı bağlantısı test edilirken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Veritabanını başlatır ve migration'ları uygular
    /// </summary>
    /// <returns>Başlatma sonucu</returns>
    public async Task<bool> InitializeDatabaseAsync()
    {
        try
        {
            // Veritabanının var olup olmadığını kontrol et
            var canConnect = await _context.Database.CanConnectAsync();
            if (!canConnect)
            {
                _logger.LogInformation("Veritabanı oluşturuluyor...");
                await _context.Database.EnsureCreatedAsync();
            }

            // Migration'ları uygula
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"{pendingMigrations.Count()} adet pending migration bulundu. Uygulanıyor...");
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Migration'lar başarıyla uygulandı.");
            }
            else
            {
                _logger.LogInformation("Uygulanacak migration bulunamadı.");
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veritabanı başlatılırken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Pending migration'ları uygular
    /// </summary>
    /// <returns>Uygulama sonucu</returns>
    public async Task<bool> ApplyMigrationsAsync()
    {
        try
        {
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"{pendingMigrations.Count()} adet pending migration uygulanıyor...");
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Migration'lar başarıyla uygulandı.");
                return true;
            }
            else
            {
                _logger.LogInformation("Uygulanacak migration bulunamadı.");
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration'lar uygulanırken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Veritabanı şemasını oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturma sonucu</returns>
    public async Task<bool> CreateSchemaAsync(string schemaName)
    {
        try
        {
            var sql = $"CREATE SCHEMA IF NOT EXISTS \"{schemaName}\"";
            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation($"Şema '{schemaName}' başarıyla oluşturuldu.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Şema '{schemaName}' oluşturulurken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Şema var mı kontrol eder
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Şema varlık durumu</returns>
    public async Task<bool> SchemaExistsAsync(string schemaName)
    {
        try
        {
            var sql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = {0}";
            var result = await _context.Database.SqlQueryRaw<int>(sql, schemaName).FirstOrDefaultAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Şema '{schemaName}' varlığı kontrol edilirken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Tablo var mı kontrol eder
    /// </summary>
    /// <param name="tableName">Tablo adı</param>
    /// <param name="schemaName">Şema adı (opsiyonel)</param>
    /// <returns>Tablo varlık durumu</returns>
    public async Task<bool> TableExistsAsync(string tableName, string? schemaName = null)
    {
        try
        {
            var schema = schemaName ?? "public";
            var sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = {0} AND table_name = {1}";
            var result = await _context.Database.SqlQueryRaw<int>(sql, schema, tableName).FirstOrDefaultAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Tablo '{tableName}' varlığı kontrol edilirken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Proje için özel şema ve tabloları oluşturur
    /// </summary>
    /// <param name="projectId">Proje ID'si</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturma sonucu</returns>
    public async Task<CreateSchemaResult> CreateProjectSchemaAsync(int projectId, string schemaName)
    {
        var result = new CreateSchemaResult
        {
            Success = false,
            Message = "",
            Schema_Created = false,
            Tables_Created = new List<string>()
        };

        try
        {
            // Şema var mı kontrol et
            var schemaExists = await SchemaExistsAsync(schemaName);
            if (!schemaExists)
            {
                // Şema oluştur
                var schemaCreated = await CreateSchemaAsync(schemaName);
                if (!schemaCreated)
                {
                    result.Message = "Şema oluşturulamadı.";
                    return result;
                }
                result.Schema_Created = true;
            }

            // Texts tablosunu oluştur
            var textsTableSql = $@"
                CREATE TABLE IF NOT EXISTS ""{schemaName}"".""{"texts"}"" (
                    ""id"" BIGSERIAL PRIMARY KEY,
                    ""row_id"" INTEGER NOT NULL,
                    ""namespace"" VARCHAR(255),
                    ""key"" VARCHAR(500),
                    ""en"" TEXT,
                    ""tr"" TEXT,
                    ""lemma"" TEXT,
                    ""note"" TEXT,
                    ""status"" VARCHAR(50),
                    ""embedding"" vector(768),
                    ""created_at"" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    ""updated_at"" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )";

            await _context.Database.ExecuteSqlRawAsync(textsTableSql);
            result.Tables_Created.Add("texts");

            // Terms tablosunu oluştur
            var termsTableSql = $@"
                CREATE TABLE IF NOT EXISTS ""{schemaName}"".""{"terms"}"" (
                    ""id"" BIGSERIAL PRIMARY KEY,
                    ""row_id"" INTEGER NOT NULL,
                    ""en"" TEXT NOT NULL,
                    ""tr"" TEXT,
                    ""category"" VARCHAR(255),
                    ""info"" TEXT,
                    ""lemma"" TEXT,
                    ""status"" VARCHAR(50),
                    ""embedding"" vector(768),
                    ""created_at"" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    ""updated_at"" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )";

            await _context.Database.ExecuteSqlRawAsync(termsTableSql);
            result.Tables_Created.Add("terms");

            result.Success = true;
            result.Message = $"Proje şeması '{schemaName}' başarıyla oluşturuldu.";

            _logger.LogInformation($"Proje {projectId} için şema '{schemaName}' ve tablolar başarıyla oluşturuldu.");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Proje {projectId} için şema '{schemaName}' oluşturulurken hata oluştu.");
            result.Message = $"Şema oluşturulurken hata oluştu: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// Şemayı siler
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Silme sonucu</returns>
    public async Task<bool> DropSchemaAsync(string schemaName)
    {
        try
        {
            var sql = $"DROP SCHEMA IF EXISTS \"{schemaName}\" CASCADE";
            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation($"Şema '{schemaName}' başarıyla silindi.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Şema '{schemaName}' silinirken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Veritabanı istatistiklerini getirir
    /// </summary>
    /// <returns>İstatistik bilgileri</returns>
    public async Task<Dictionary<string, object>> GetDatabaseStatisticsAsync()
    {
        var stats = new Dictionary<string, object>();

        try
        {
            // Veritabanı boyutu
            var dbSizeSql = "SELECT pg_size_pretty(pg_database_size(current_database())) as size";
            var dbSize = await _context.Database.SqlQueryRaw<string>(dbSizeSql).FirstOrDefaultAsync();
            stats["database_size"] = dbSize ?? "Unknown";

            // Tablo sayısı
            var tableCountSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'pg_catalog')";
            var tableCount = await _context.Database.SqlQueryRaw<int>(tableCountSql).FirstOrDefaultAsync();
            stats["table_count"] = tableCount;

            // Şema sayısı
            var schemaCountSql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')";
            var schemaCount = await _context.Database.SqlQueryRaw<int>(schemaCountSql).FirstOrDefaultAsync();
            stats["schema_count"] = schemaCount;

            // Aktif bağlantı sayısı
            var connectionCountSql = "SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'";
            var connectionCount = await _context.Database.SqlQueryRaw<int>(connectionCountSql).FirstOrDefaultAsync();
            stats["active_connections"] = connectionCount;

            stats["last_updated"] = DateTime.UtcNow;

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veritabanı istatistikleri alınırken hata oluştu.");
            stats["error"] = ex.Message;
            return stats;
        }
    }

    /// <summary>
    /// Raw SQL sorgusu çalıştırır
    /// </summary>
    /// <param name="sql">SQL sorgusu</param>
    /// <param name="parameters">Parametreler</param>
    /// <returns>Sorgu sonucu</returns>
    public async Task<List<Dictionary<string, object>>> ExecuteRawSqlAsync(string sql, params object[] parameters)
    {
        var results = new List<Dictionary<string, object>>();

        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);

            // Parametreleri ekle
            for (int i = 0; i < parameters.Length; i++)
            {
                command.Parameters.AddWithValue($"@p{i}", parameters[i]);
            }

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    row[reader.GetName(i)] = reader.GetValue(i);
                }
                results.Add(row);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Raw SQL sorgusu çalıştırılırken hata oluştu: {Sql}", sql);
            throw;
        }
    }

    /// <summary>
    /// Veritabanı backup'ı alır
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Backup sonucu</returns>
    public async Task<bool> CreateBackupAsync(string backupPath)
    {
        try
        {
            // Bu metot pg_dump kullanarak backup alabilir
            // Şimdilik basit bir implementasyon
            _logger.LogWarning("Backup özelliği henüz implementasyona alınmamıştır.");
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Backup alınırken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Backup'tan veritabanını geri yükler
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Geri yükleme sonucu</returns>
    public async Task<bool> RestoreBackupAsync(string backupPath)
    {
        try
        {
            // Bu metot pg_restore kullanarak geri yükleme yapabilir
            // Şimdilik basit bir implementasyon
            _logger.LogWarning("Restore özelliği henüz implementasyona alınmamıştır.");
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Restore işlemi sırasında hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Belirtilen şema için dinamik DbContext oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Dinamik şema DbContext'i</returns>
    public DynamicSchemaDbContext CreateDynamicContext(string schemaName)
    {
        var optionsBuilder = new DbContextOptionsBuilder<DynamicSchemaDbContext>();
        optionsBuilder.UseNpgsql(_connectionString);


        return new DynamicSchemaDbContext(optionsBuilder.Options, schemaName);
    }

}