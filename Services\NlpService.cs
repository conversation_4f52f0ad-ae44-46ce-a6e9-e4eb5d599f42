using Catalyst;
using Mosaik.Core;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using TranslationAgentServer.Interfaces;
using System.Linq;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services
{
    /// <summary>
    /// Catalyst kütü<PERSON><PERSON>ini kullanarak doğal dil işleme operasyonları sağlayan servis
    /// </summary>
    public class NlpService : INlpService
    {
        private readonly ILogger<NlpService> _logger;
        private Pipeline? _nlpPipeline;
        private bool _isInitialized = false;
        private readonly SemaphoreSlim _initializationSemaphore = new(1, 1);

        public bool IsReady => _isInitialized && _nlpPipeline != null;

        public NlpService(ILogger<NlpService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// NLP pipeline'ını başlatır ve hazırlar
        /// </summary>
        /// <returns>Başlatma işleminin başarılı olup olmadığını belirten Task</returns>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
                return true;

            await _initializationSemaphore.WaitAsync();
            try
            {
                if (_isInitialized)
                    return true;

                _logger.LogInformation("NLP Pipeline başlatılıyor...");

                // Catalyst modellerini kaydet
                Catalyst.Models.English.Register();

                // Storage ayarla (modeller için)
                Storage.Current = new DiskStorage("catalyst-models");

                // Pipeline'ı başlat
                _nlpPipeline = await Pipeline.ForAsync(Language.English);

                _isInitialized = true;
                _logger.LogInformation("NLP Pipeline başarıyla başlatıldı.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "NLP Pipeline başlatılırken hata oluştu.");
                return false;
            }
            finally
            {
                _initializationSemaphore.Release();
            }
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <returns>Lemmatized metin</returns>
        public async Task<string> ProcessTextToLemmaAsync(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (!IsReady)
            {
                var initialized = await InitializeAsync();
                if (!initialized)
                {
                    _logger.LogWarning("NLP Pipeline başlatılamadı, orijinal metin döndürülüyor.");
                    return text.Trim();
                }
            }

            try
            {
                // Metni küçük harfe çevir ve İngilizce locale kullan
                var sentenceDoc = new Document(text.ToLower(CultureInfo.GetCultureInfo("en-GB")), Language.English);

                // NLP işlemini uygula
                _nlpPipeline!.ProcessSingle(sentenceDoc);

                // Lemma'ları çıkar ve noktalama işaretlerini filtrele
                var lemmas = sentenceDoc.SelectMany(s => s)
                    .Where(t => t.POS != PartOfSpeech.PUNCT && !string.IsNullOrWhiteSpace(t.Lemma))
                    .Select(t => t.Lemma);

                var lemmatizedText = string.Join(" ", lemmas);

                // Çoklu boşlukları tek boşluğa dönüştür ve trim et
                return Regex.Replace(lemmatizedText, @"\s+", " ").Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Metin lemmatization işlemi sırasında hata oluştu: {Text}", text);
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }

        /// <summary>
        /// Verilen metni analiz eder ve detaylı bilgi döndürür
        /// </summary>
        /// <param name="text">Analiz edilecek metin</param>
        /// <returns>Metin analiz sonucu</returns>
        public async Task<TextAnalysisResult> AnalyzeTextAsync(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return new TextAnalysisResult { ErrorMessage = "Metin boş veya geçersiz" };

            if (!IsReady)
            {
                var initialized = await InitializeAsync();
                if (!initialized)
                {
                    return new TextAnalysisResult { ErrorMessage = "NLP Pipeline başlatılamadı" };
                }
            }

            try
            {
                var doc = new Document(text, Language.English);
                _nlpPipeline!.ProcessSingle(doc);

                var analysis = new TextAnalysisResult
                {
                    IsSuccess = true,
                    OriginalText = text,
                    CharacterCount = text.Length,
                    WordCount = doc.SelectMany(s => s.Select(t => t.POS != PartOfSpeech.PUNCT && !string.IsNullOrWhiteSpace(t.Value))).Count(),
                    SentenceCount = doc.SelectMany(s => s).Count(),
                    LemmatizedText = await ProcessTextToLemmaAsync(text)
                };

                return analysis;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Metin analizi sırasında hata oluştu: {Text}", text);
                return new TextAnalysisResult { ErrorMessage = "Metin analizi sırasında hata oluştu", ErrorDetails = ex.Message };
            }
        }

        /// <summary>
        /// Servisi temizler ve kaynakları serbest bırakır
        /// </summary>
        public void Dispose()
        {
            _initializationSemaphore?.Dispose();
        }
    }
}