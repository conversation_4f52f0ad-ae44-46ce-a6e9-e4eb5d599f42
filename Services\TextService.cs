using Supabase;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;


namespace TranslationAgentServer.Services;

/// <summary>
/// Text servisi implementasyonu
/// Metin verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
/// </summary>
public class TextService : ITextService
{
    private readonly ISupabaseService _supabaseService;
    private readonly INlpService _nlpService;
    private readonly IGeminiService _geminiService;
    private readonly EmbeddingHelper _embeddingHelper;
    private readonly ILogger<TextService> _logger;

    public TextService(
        ISupabaseService supabaseService,
        INlpService nlpService,
        IGeminiService geminiService,
        EmbeddingHelper embeddingHelper,
        ILogger<TextService> logger)
    {
        _supabaseService = supabaseService;
        _nlpService = nlpService;
        _geminiService = geminiService;
        _embeddingHelper = embeddingHelper;
        _logger = logger;
    }

    public async Task<List<Text>> GetAllTextsAsync(long ProjectId, int page = 1, int pageSize = 50,
        string? namespaceFilter = null, string? keyFilter = null, string? enFilter = null,
        string? trFilter = null, TextStatus? statusFilter = null)
    {
        try
        {
            var client = await _supabaseService.GetProjectClient(ProjectId);
            var offset = (page - 1) * pageSize;

            // Önce tüm verileri al
            var query = client
                .From<Text>().Where(c => c != null);

            // Filtreleri uygula
            if (!string.IsNullOrWhiteSpace(namespaceFilter))
            {
                query = query.Where(x => x.Namespace != null && x.Namespace.Contains(namespaceFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(keyFilter))
            {
                query = query.Where(x => x.Key != null && x.Key.Contains(keyFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(enFilter))
            {
                query = query.Where(x => x.En != null && x.En.Contains(enFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(trFilter))
            {
                query = query.Where(x => x.Tr != null && x.Tr.Contains(trFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (statusFilter.HasValue)
            {
                query = query.Where(x => x.Status == statusFilter.Value);
            }

            var result = await query
                          .Range(offset, offset + pageSize - 1)
                          .Get();
            return result.Models;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metinler getirilirken hata oluştu. Şema: {ProjectId}, Sayfa: {Page}, Boyut: {PageSize}, Filtreler: Namespace={Namespace}, Key={Key}, En={En}, Tr={Tr}, Status={Status}",
                ProjectId, page, pageSize, namespaceFilter, keyFilter, enFilter, trFilter, statusFilter);
            throw;
        }
    }

    public async Task<Text?> GetTextByIdAsync(long id, long ProjectId)
    {
        try
        {
            var client = await _supabaseService.GetProjectClient(ProjectId);
            var response = await client
                .From<Text>()
                .Where(x => x.Id == id)
                .Single();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin getirilirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return null;
        }
    }

    public async Task<List<Text>> SearchTextsAsync(string searchTerm, long ProjectId, int page = 1, int pageSize = 50)
    {
        try
        {
            // PostgreSQL full-text search kullanarak arama
            var client = await _supabaseService.GetProjectClient(ProjectId);
            var offset = (page - 1) * pageSize;

            var response = await client
                .From<Text>()
                .Where(x => x.En!.Contains(searchTerm) || x.Tr!.Contains(searchTerm) || x.Key!.Contains(searchTerm))
                .Range(offset, offset + pageSize - 1)
                .Get();

            return response.Models;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin arama işleminde hata oluştu. SearchTerm: {SearchTerm}, Şema: {ProjectId}, Sayfa: {Page}, Boyut: {PageSize}", searchTerm, ProjectId, page, pageSize);
            throw;
        }
    }

    public async Task<List<Text>> FindSimilarTextsAsync(float[] embedding, int limit, long ProjectId)
    {
        try
        {
            // Embedding benzerlik araması için özel SQL sorgusu gerekebilir
            // Şimdilik basit bir implementasyon
            var allTexts = await GetAllTextsAsync(ProjectId);

            // Embedding'i olan metinleri filtrele ve benzerlik hesapla
            var textsWithEmbedding = allTexts
                .Where(t => t.Embedding != null && t.Embedding.Length > 0)
                .Select(t => new { Text = t, Similarity = EmbeddingHelper.CalculateCosineSimilarity(embedding, t.Embedding!) })
                .OrderByDescending(x => x.Similarity)
                .Take(limit)
                .Select(x => x.Text)
                .ToList();

            return textsWithEmbedding;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer metin arama işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            throw;
        }
    }

    public async Task<Text> CreateTextAsync(TextCreateDto textCreateDto, long ProjectId)
    {
        try
        {
            var text = new Text
            {
                RowID = textCreateDto.RowID,
                Namespace = textCreateDto.Namespace,
                Key = textCreateDto.Key,
                En = textCreateDto.En,
                Tr = textCreateDto.Tr,
                Lemma = textCreateDto.Lemma,
                Note = textCreateDto.Note,
                Status = textCreateDto.Status,
                Embedding = textCreateDto.Embedding,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Eğer embedding yoksa ve İngilizce metin varsa, embedding oluştur
            if (text.Embedding == null && !string.IsNullOrEmpty(text.En) &&
                text.Status != TextStatus.DUPE && text.Status != TextStatus.NULL)
            {
                text.Embedding = await _embeddingHelper.GenerateEmbeddingAsync(text.En);
            }

            // Eğer lemma yoksa ve İngilizce metin varsa, lemmatization yap
            if (string.IsNullOrEmpty(text.Lemma) && !string.IsNullOrEmpty(text.En))
            {
                text.Lemma = await _nlpService.ProcessTextToLemmaAsync(text.En);
            }

            var client = await _supabaseService.GetProjectClient(ProjectId);
            var response = await client
                .From<Text>()
                .Insert(text);

            return response.Model!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin oluşturulurken hata oluştu. Şema: {ProjectId}", ProjectId);
            throw;
        }
    }

    public async Task<Text?> UpdateTextAsync(long id, TextUpdateDto textUpdateDto, long ProjectId)
    {
        try
        {
            var existingText = await GetTextByIdAsync(id, ProjectId);
            if (existingText == null)
            {
                return null;
            }

            // Güncelleme alanlarını ayarla
            if (textUpdateDto.Namespace != null) existingText.Namespace = textUpdateDto.Namespace;
            if (textUpdateDto.Key != null) existingText.Key = textUpdateDto.Key;
            if (textUpdateDto.En != null) existingText.En = textUpdateDto.En;
            if (textUpdateDto.Tr != null) existingText.Tr = textUpdateDto.Tr;
            if (textUpdateDto.Lemma != null) existingText.Lemma = textUpdateDto.Lemma;
            if (textUpdateDto.Note != null) existingText.Note = textUpdateDto.Note;
            if (textUpdateDto.Status.HasValue) existingText.Status = textUpdateDto.Status;
            if (textUpdateDto.Embedding != null) existingText.Embedding = textUpdateDto.Embedding;

            existingText.UpdatedAt = DateTime.UtcNow;

            // Eğer İngilizce metin güncellendiyse, embedding ve lemma'yı yeniden oluştur
            if (textUpdateDto.En != null)
            {
                existingText.Embedding = await _embeddingHelper.GenerateEmbeddingAsync(textUpdateDto.En);
                existingText.Lemma = await _nlpService.ProcessTextToLemmaAsync(textUpdateDto.En);
            }

            var client = await _supabaseService.GetProjectClient(ProjectId);
            var response = await client
                .From<Text>()
                .Where(x => x.Id == id)
                .Update(existingText);

            return response.Model;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin güncellenirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            throw;
        }
    }

    public async Task<bool> DeleteTextAsync(long id, long ProjectId)
    {
        try
        {
            var client = await _supabaseService.GetProjectClient(ProjectId);
            var response = client
                .From<Text>()
                .Where(x => x.Id == id)
                .Delete();

            return response.IsCompletedSuccessfully;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin silinirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return false;
        }
    }

    public async Task<bool> CreateTextsAsync(List<TextCreateDto> textCreateDtos, long ProjectId)
    {
        try
        {
            var client = await _supabaseService.GetProjectClient(ProjectId);
            var existingTexts = await client
                 .From<Text>().Select("*").Where(t => t.Embedding != null).Get();

            var texts = textCreateDtos.Select(dto => new Text
            {
                RowID = dto.RowID,
                Namespace = dto.Namespace,
                Key = dto.Key,
                En = dto.En,
                Tr = dto.Tr,
                Lemma = string.IsNullOrEmpty(dto.Lemma) && !string.IsNullOrEmpty(dto.En)
     ? Task.Run(async () => await _nlpService.ProcessTextToLemmaAsync(dto.En)).Result
     : dto.Lemma,
                Note = dto.Note,
                Status = dto.Status,
                Embedding = existingTexts.Models.FirstOrDefault(t => t.En!.Equals(dto.En, StringComparison.Ordinal))?.Embedding ?? dto.Embedding,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            //Batch embedding işlemi (100'lük gruplar) - retry mekanizması ile
            var batchSize = 100;
            for (int i = 0; i < texts.Count; i += batchSize)
            {
                var batch = texts.Skip(i).Take(batchSize).ToList();

                // Retry mekanizması ile embedding üretimi
                var embeddings = await Helpers.RetryHelper.ExecuteWithRetryAsync(
                    operation: async () => await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList()),
                    maxRetries: 3,
                    baseDelayMs: 2000,
                    logger: _logger,
                    operationName: $"BatchEmbedding(batch {i / batchSize + 1}, size: {batch.Count})"
                );

                for (int j = 0; j < batch.Count; j++)
                {
                    batch[j].Embedding = embeddings[j].ToArray();
                }

                // Batch'ler arası bekleme süresi (rate limiting için)
                await Task.Delay(3500);
            }

            await client.From<Text>().Select("*").Delete();

            var response = await client
                      .From<Text>()
                      .Insert(texts);
            return response?.ResponseMessage?.IsSuccessStatusCode ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin oluşturma işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            throw;
        }
    }

    public async Task<List<Text>> UpdateTextsAsync(List<(long Id, TextUpdateDto UpdateDto)> textUpdates, long ProjectId)
    {
        try
        {
            var updatedTexts = new List<Text>();

            foreach (var (id, updateDto) in textUpdates)
            {
                var updatedText = await UpdateTextAsync(id, updateDto, ProjectId);
                if (updatedText != null)
                {
                    updatedTexts.Add(updatedText);
                }
            }

            return updatedTexts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin güncelleme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            throw;
        }
    }

    public async Task<bool> DeleteTextsAsync(List<long> ids, long ProjectId)
    {
        try
        {
            foreach (var id in ids)
            {
                await DeleteTextAsync(id, ProjectId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin silme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return false;
        }
    }


    public async Task<TextStatistics> GetTextStatisticsAsync(long ProjectId)
    {
        try
        {
            var allTexts = await GetAllTextsAsync(ProjectId);

            var statistics = new TextStatistics
            {
                TotalTexts = allTexts.Count,
                EnglishTexts = allTexts.Count(t => t.Status == TextStatus.EN),
                TurkishTexts = allTexts.Count(t => t.Status == TextStatus.TR),
                DuplicateTexts = allTexts.Count(t => t.Status == TextStatus.DUPE),
                NullTexts = allTexts.Count(t => t.Status == TextStatus.NULL),
                TextsWithEmbedding = allTexts.Count(t => t.Embedding != null && t.Embedding.Length > 0),
                LemmatizedTexts = allTexts.Count(t => !string.IsNullOrEmpty(t.Lemma)),
                NamespaceDistribution = allTexts
                    .Where(t => !string.IsNullOrEmpty(t.Namespace))
                    .GroupBy(t => t.Namespace!)
                    .ToDictionary(g => g.Key, g => g.Count()),
                SheetDistribution = allTexts
                    .GroupBy(t => t.RowID)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin istatistikleri hesaplanırken hata oluştu. Şema: {ProjectId}", ProjectId);
            throw;
        }
    }
}