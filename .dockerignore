# .dockerignore - Docker build sırasında dahil edilmeyecek dosyalar
# Performansı artırır ve image boyutunu küçültür

# Build artifacts
bin/
obj/

# Visual Studio files
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Git files
.git/
.gitignore
.gitattributes

# Documentation
*.md
README*

# Test files
**/TestResults/
**/*Test*/
**/*Tests*/

# Logs
logs/
*.log

# Runtime files
*.tmp
*.temp

# IDE files
.vscode/
.idea/

# OS files
Thumbs.db
.DS_Store

# Package files
*.nupkg
*.snupkg

# Docker files (kendini dahil etme)
Dockerfile*
.dockerignore