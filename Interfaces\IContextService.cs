using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Context servisi arayüzü
/// Context verilerinin CRUD işlemlerini ve özel sorguları tanımlar
/// </summary>
public interface IContextService
{
    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    /// <param name="projectId"><PERSON>je kimliği</param>
    /// <param name="page"><PERSON><PERSON> numarası (1'den başlar)</param>
    /// <param name="pageSize">Say<PERSON> boyutu</param>
    /// <param name="categoryFilter">Kategori filtresi</param>
    /// <param name="titleFilter">Başlık filtresi</param>
    /// <param name="contentFilter">İçerik filtresi</param>
    /// <returns>Context listesi</returns>
    Task<List<Context>> GetAllContextsAsync(long projectId, int page = 1, int pageSize = 50,
        string? categoryFilter = null, string? titleFilter = null, string? contentFilter = null);

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context</returns>
    Task<Context?> GetContextByIdAsync(long id, long projectId);

    /// <summary>
    /// Anahtar kelimeye göre context arar (başlık ve içerikte)
    /// </summary>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="page">Sayfa numarası (1'den başlar)</param>
    /// <param name="pageSize">Sayfa boyutu</param>
    /// <returns>Context listesi</returns>
    Task<List<Context>> SearchContextsAsync(string searchTerm, long projectId, int page = 1, int pageSize = 50);

    /// <summary>
    /// Full-text search kullanarak context arar
    /// </summary>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="page">Sayfa numarası (1'den başlar)</param>
    /// <param name="pageSize">Sayfa boyutu</param>
    /// <returns>Context listesi</returns>
    Task<List<Context>> FullTextSearchContextsAsync(string searchTerm, long projectId, int page = 1, int pageSize = 50);

    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Benzer context listesi</returns>
    Task<List<Context>> FindSimilarContextsAsync(float[] embedding, int limit, long projectId);

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    /// <param name="contextCreateDto">Context oluşturma DTO'su</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Oluşturulan context</returns>
    Task<Context> CreateContextAsync(ContextCreateDto contextCreateDto, long projectId);

    /// <summary>
    /// Context günceller
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="contextUpdateDto">Context güncelleme DTO'su</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Güncellenmiş context</returns>
    Task<Context?> UpdateContextAsync(long id, ContextUpdateDto contextUpdateDto, long projectId);

    /// <summary>
    /// Context siler
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteContextAsync(long id, long projectId);

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    /// <param name="contextCreateDtos">Context oluşturma DTO listesi</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Oluşturulan context listesi</returns>
    Task<List<Context>> CreateContextsAsync(List<ContextCreateDto> contextCreateDtos, long projectId);

    /// <summary>
    /// Context'leri toplu olarak günceller
    /// </summary>
    /// <param name="contextUpdates">Context güncelleme listesi (ID ve DTO çiftleri)</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Güncellenmiş context listesi</returns>
    Task<List<Context>> UpdateContextsAsync(List<(long Id, ContextUpdateDto UpdateDto)> contextUpdates, long projectId);

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    /// <param name="ids">Silinecek context ID'leri</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteContextsAsync(List<long> ids, long projectId);

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context istatistikleri</returns>
    Task<ContextStatistics> GetContextStatisticsAsync(long projectId);
}