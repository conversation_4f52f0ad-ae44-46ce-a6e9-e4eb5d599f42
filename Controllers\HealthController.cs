using Microsoft.AspNetCore.Mvc;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Render.com ve diğer monitoring servisleri için health check endpoint'i sağlar
/// Uygulamanın sağlık durumunu kontrol etmek için kullanılır
/// </summary>
[ApiController]
[Route("[controller]")]
public class HealthController : ControllerBase
{
    /// <summary>
    /// Uygulamanın sağlık durumunu kontrol eder
    /// Render.com tarafından otomatik olarak çağrılır
    /// </summary>
    /// <returns>Uygulamanın sağlık durumu</returns>
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { 
            status = "healthy", 
            timestamp = DateTime.UtcNow,
            service = "TranslationAgentServer",
            version = "1.0.0"
        });
    }

    /// <summary>
    /// Detaylı sağlık kontrolü endpoint'i
    /// Sistem kaynaklarını ve bağımlılıkları kontrol eder
    /// </summary>
    /// <returns>Detaylı sağlık durumu bilgisi</returns>
    [HttpGet("detailed")]
    public IActionResult GetDetailed()
    {
        var healthInfo = new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            service = "TranslationAgentServer",
            version = "1.0.0",
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            uptime = Environment.TickCount64,
            memoryUsage = GC.GetTotalMemory(false),
            processorCount = Environment.ProcessorCount
        };

        return Ok(healthInfo);
    }
}