using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Options;
using Mscc.GenerativeAI;
using TranslationAgentServer.Configurations;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
namespace TranslationAgentServer.Services;

/// <summary>
/// Gemini AI servisi - Google'ın generative AI modellerini kullanarak içerik üretimi ve embedding işlemleri yapar
/// </summary>
public class GeminiService : IGeminiService
{

    private GoogleAI? _googleAI;
    private readonly ILogger<GeminiService> _logger;
    private bool _isInitialized = false;
    private GeminiOptions _options;
    private HttpClient _httpClient;

    public GeminiService(ILogger<GeminiService> logger)
    {
        _logger = logger;
        _logger.LogInformation("Gemini servis oluşturuldu.");
    }

    public bool InitializeAsync(string apiKey)
    {
        if (_isInitialized && _googleAI != null)
            return false;

        try
        {

            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("Gemini API anahtarı bulunamadı. Lütfen appsettings.json'da veya Supabase main tablosunda 'gemini_api' anahtarını yapılandırın.");
            }
            _options = new GeminiOptions
            {
                ApiKey = apiKey,
                BaseUrl = "https://generativelanguage.googleapis.com/v1beta",
                TimeoutSeconds = 30
            };
            _googleAI = new GoogleAI(apiKey);
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds)
            };
            _isInitialized = true;
            _logger.LogInformation("Gemini servis başarıyla başlatıldı.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gemini servis başlatılırken hata oluştu.");
            throw;
        }
    }



    /// <summary>
    /// Belirtilen model ve ayarlarla metin içeriği üretir
    /// </summary>
    public async Task<GeminiContentResponse> GenerateContentAsync(GeminiContentRequest request)
    {


        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            // Model seçimi
            var modelName = request.Model ?? Model.Gemini25Flash;
            var model = _googleAI.GenerativeModel(model: modelName);

            // Sistem talimatı varsa ekle
            if (!string.IsNullOrEmpty(request.SystemInstruction))
            {
                var systemInstruction = new Mscc.GenerativeAI.Content(request.SystemInstruction);
                model = _googleAI.GenerativeModel(model: modelName ?? Model.Gemini25Flash, systemInstruction: systemInstruction);
            }

            // Grounding ayarları
            if (request.UseGrounding)
            {
                model.UseGrounding = true;
            }

            // Generation config ayarları
            GenerationConfig? generationConfig = null;
            if (request.MaxTokens.HasValue || request.Temperature.HasValue ||
                request.TopP.HasValue || request.TopK.HasValue || request.RequireJsonOutput)
            {
                generationConfig = new GenerationConfig();

                if (request.MaxTokens.HasValue)
                    generationConfig.MaxOutputTokens = request.MaxTokens.Value;

                if (request.Temperature.HasValue)
                    generationConfig.Temperature = request.Temperature.Value;

                if (request.TopP.HasValue)
                    generationConfig.TopP = request.TopP.Value;

                if (request.TopK.HasValue)
                    generationConfig.TopK = request.TopK.Value;

                if (request.RequireJsonOutput)
                    generationConfig.ResponseMimeType = "application/json";
            }

            // İçerik üretimi
            var response = generationConfig != null
                ? await model.GenerateContent(request.Prompt, generationConfig: generationConfig)
                : await model.GenerateContent(request.Prompt);

            stopwatch.Stop();

            _logger.LogInformation("Gemini içerik üretimi tamamlandı. Süre: {Duration}ms, Model: {Model}",
                stopwatch.ElapsedMilliseconds, modelName);

            return new GeminiContentResponse
            {
                Text = response.Text ?? string.Empty,
                Model = modelName,
                Success = true,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Gemini içerik üretimi sırasında hata oluştu.");

            return new GeminiContentResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }


    /// <summary>
    /// Tekli metin için embedding üretir
    /// </summary>
    public async Task<GeminiEmbeddingResponse> GenerateEmbeddingAsync(string text, string? model = null)
    {

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            var embeddingModel = model ?? Model.TextEmbedding;
            var geminiModel = _googleAI.GenerativeModel(model: embeddingModel);

            var response = await geminiModel.EmbedContent(text);

            stopwatch.Stop();

            _logger.LogInformation("Gemini embedding üretimi tamamlandı. Süre: {Duration}ms, Model: {Model}",
                stopwatch.ElapsedMilliseconds, embeddingModel);

            var embedding = response.Embedding?.Values?.ToArray() ?? Array.Empty<float>();

            return new GeminiEmbeddingResponse
            {
                Embedding = embedding,
                Model = embeddingModel,
                Success = true,
                Dimensions = embedding.Length,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Gemini embedding üretimi sırasında hata oluştu.");

            return new GeminiEmbeddingResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// Çoklu metin için embedding üretir
    /// </summary>
    public async Task<GeminiMultipleEmbeddingResponse> GenerateMultipleEmbeddingsAsync(List<string> texts, string? model = null)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            var embeddingModel = model ?? Model.TextEmbedding;
            var geminiModel = _googleAI.GenerativeModel(model: embeddingModel);

            var embeddings = new List<float[]>();

            var response = await geminiModel.EmbedContent(texts);
            if (response.Embeddings == null)
            {
                throw new Exception("Embedding üretilemedi");
            }

            embeddings.AddRange(response.Embeddings.Select(e => e.Values?.ToArray() ?? Array.Empty<float>()).ToList());

            stopwatch.Stop();

            _logger.LogInformation("Gemini çoklu embedding üretimi tamamlandı. Süre: {Duration}ms, Model: {Model}, Metin Sayısı: {Count}",
                stopwatch.ElapsedMilliseconds, embeddingModel, texts.Count);

            var dimensions = embeddings.FirstOrDefault()?.Length ?? 0;

            return new GeminiMultipleEmbeddingResponse
            {
                Embeddings = embeddings,
                Model = embeddingModel,
                Success = true,
                Dimensions = dimensions,
                TextCount = texts.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Gemini çoklu embedding üretimi sırasında hata oluştu.");

            return new GeminiMultipleEmbeddingResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                TextCount = texts.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// Çoklu metin için embedding üretir (retry mekanizması ile)
    /// </summary>
    public async Task<List<List<float>>> GetEmbeddingsAsync(List<string> texts)
    {
        if (texts == null || texts.Count == 0)
            throw new ArgumentException("Texts cannot be null or empty", nameof(texts));

        _logger.LogInformation("Getting embeddings for {Count} texts", texts.Count);

        return await Helpers.RetryHelper.ExecuteWithRetryAsync(
            operation: async () => await GetEmbeddingsInternalAsync(texts),
            maxRetries: 3,
            baseDelayMs: 2000,
            logger: _logger,
            operationName: $"GetEmbeddings({texts.Count} texts)"
        );
    }

    /// <summary>
    /// Embedding üretimi için internal metod
    /// </summary>
    private async Task<List<List<float>>> GetEmbeddingsInternalAsync(List<string> texts)
    {
        var requests = new List<EmbedContentRequest>();
        foreach (var text in texts)
        {
            requests.Add(new EmbedContentRequest
            {
                model = "models/" + Model.GeminiEmbedding,
                content = new Content
                {
                    parts = new List<Part>
                        {
                            new Part { text = text }
                        }
                },
                outputDimensionality = 768,
                taskType = "SEMANTIC_SIMILARITY"
            });
        }

        var batchRequest = new BatchEmbedRequest
        {
            requests = requests
        };

        var jsonRequest = JsonSerializer.Serialize(batchRequest, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        var url = $"{_options.BaseUrl}/models/" + Model.GeminiEmbedding + $":batchEmbedContents?key={_options.ApiKey}";
        var httpContent = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync(url, httpContent);
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError("API request failed with status {StatusCode}: {ResponseContent}",
                response.StatusCode, responseContent);
            throw new HttpRequestException($"API request failed with status {response.StatusCode}: {responseContent}");
        }

        var embeddingResponse = JsonSerializer.Deserialize<BatchEmbedResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        var results = new List<List<float>>();
        foreach (var embedding in embeddingResponse.embeddings)
        {
            double magnitude = Math.Sqrt(embedding.values.Sum(x => x * x));

            // Normalize edilmiş vektörü oluştur
            List<float> normalizedEmbedding = embedding.values
                .Select(x => (float)(x / magnitude))
                .ToList();

            results.Add(normalizedEmbedding);
        }

        _logger.LogInformation("Successfully retrieved {Count} embeddings", results.Count);
        return results;
    }


    // Configuration model
    public class GeminiOptions
    {
        public string ApiKey { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = "https://generativelanguage.googleapis.com/v1beta";
        public int TimeoutSeconds { get; set; } = 300;
    }

    // Request/Response models
    public class BatchEmbedRequest
    {
        public List<EmbedContentRequest> requests { get; set; }
    }

    public class EmbedContentRequest
    {
        public string model { get; set; }
        public Content content { get; set; }
        public int? outputDimensionality { get; set; }
        public string? taskType { get; set; }
    }

    public class Content
    {
        public List<Part> parts { get; set; }
    }

    public class Part
    {
        public string text { get; set; }
    }

    public class BatchEmbedResponse
    {
        public List<EmbeddingResult> embeddings { get; set; }
    }

    public class EmbeddingResult
    {
        public List<float> values { get; set; }
    }
}