using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Supabase;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// Context servisi implementasyonu
/// Context verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
/// </summary>
public class ContextService : IContextService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILogger<ContextService> _logger;

    public ContextService(IDatabaseService databaseService, ILogger<ContextService> logger)
    {
        _databaseService = databaseService;
        _logger = logger;
    }

    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    public async Task<List<Context>> GetAllContextsAsync(int projectId, int page = 1, int pageSize = 50,
        string? categoryFilter = null, string? titleFilter = null, string? contentFilter = null)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var offset = (page - 1) * pageSize;

            // Önce tüm verileri al
            var query = client.Contexts.Where(c => c != null);

            // Filtreleri uygula
            if (!string.IsNullOrWhiteSpace(categoryFilter))
            {
                query = query.Where(x => x.Category != null && x.Category.Contains(categoryFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(titleFilter))
            {
                query = query.Where(x => x.Title != null && x.Title.Contains(titleFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(contentFilter))
            {
                query = query.Where(x => x.Content != null && x.Content.Contains(contentFilter, StringComparison.OrdinalIgnoreCase));
            }

            var result = await query
                          .Skip(offset).Take(pageSize).ToListAsync();
            return result ?? new List<Context>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler getirilirken hata oluştu. Şema: {projectId}, Sayfa: {Page}, Boyut: {PageSize}, Filtreler: Category={Category}, Title={Title}, Content={Content}",
                projectId, page, pageSize, categoryFilter, titleFilter, contentFilter);
            throw;
        }
    }

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    public async Task<Context?> GetContextByIdAsync(int id, int projectId)
    {
        try
        {
            var response = await _databaseService.GetProjectContext(projectId).Contexts
                .Where(x => x.Id == id)
                .SingleAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context getirilirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return null;
        }
    }


    /// <summary>
    /// Anahtar kelimeye göre context arar
    /// </summary>
    public async Task<List<Context>> SearchContextsAsync(string searchTerm, int projectId, int page = 1, int pageSize = 50)
    {
        try
        {

            var startIndex = (page - 1) * pageSize;
            var endIndex = startIndex + pageSize - 1;

            var response = await _databaseService.GetProjectContext(projectId).Contexts
                .Where(x => x.Title!.Contains(searchTerm) || x.Content!.Contains(searchTerm) || x.Category!.Contains(searchTerm))
                .Skip(startIndex).Take(pageSize)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context arama işleminde hata oluştu. SearchTerm: {SearchTerm}, Schema: {projectId}, Page: {Page}, PageSize: {PageSize}",
                searchTerm, projectId, page, pageSize);
            throw;
        }
    }

    /// <summary>
    /// Full-text search kullanarak context arar
    /// </summary>
    public async Task<List<Context>> FullTextSearchContextsAsync(string searchTerm, int projectId, int page = 1, int pageSize = 50)
    {
        try
        {


            var startIndex = (page - 1) * pageSize;
            var endIndex = startIndex + pageSize - 1;

            // PostgreSQL full-text search kullanarak arama
            var response = await _databaseService.GetProjectContext(projectId).Contexts
                .Where(x => x.Title!.Contains(searchTerm) || x.Content!.Contains(searchTerm) || x.Category!.Contains(searchTerm))
                .Skip(startIndex).Take(pageSize)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full-text search işleminde hata oluştu. SearchTerm: {SearchTerm}, Schema: {projectId}, Page: {Page}, PageSize: {PageSize}",
                searchTerm, projectId, page, pageSize);
            throw;
        }
    }

    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    public async Task<List<Context>> FindSimilarContextsAsync(float[] embedding, int limit, int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            // Vector similarity search için RPC fonksiyonu kullanılabilir
            // Bu örnekte basit bir yaklaşım kullanıyoruz
            var response = await client.Contexts
                .Where(x => x.Embedding != null)
                .Take(limit)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer context'ler bulunurken hata oluştu. Schema: {projectId}, Limit: {Limit}",
                projectId, limit);
            throw;
        }
    }

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    public async Task<Context> CreateContextAsync(ContextCreateDto contextCreateDto, int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var context = new Context
            {
                Category = contextCreateDto.Category,
                Title = contextCreateDto.Title,
                Content = contextCreateDto.Content,
                Embedding = contextCreateDto.Embedding,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var response = await client.Contexts.AddAsync(context);

            return response.Entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context oluşturulurken hata oluştu. Schema: {projectId}, Title: {Title}",
                projectId, contextCreateDto.Title);
            throw;
        }
    }

    /// <summary>
    /// Context günceller
    /// </summary>
    public async Task<Context?> UpdateContextAsync(int id, ContextUpdateDto contextUpdateDto, int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Contexts
                .Where(x => x.Id == id)
                .ExecuteUpdateAsync(s => s
                    .SetProperty(c => c.UpdatedAt, DateTime.UtcNow)
                    .SetProperty(c => c.Category, contextUpdateDto.Category != null ? contextUpdateDto.Category : c.Category)
                    .SetProperty(c => c.Title, contextUpdateDto.Title != null ? contextUpdateDto.Title : c.Title)
                    .SetProperty(c => c.Content, contextUpdateDto.Content != null ? contextUpdateDto.Content : c.Content)
                    .SetProperty(c => c.Embedding, contextUpdateDto.Embedding != null ? contextUpdateDto.Embedding : c.Embedding)
                );

            return response.Entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context güncellenirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return null;
        }
    }

    /// <summary>
    /// Context siler
    /// </summary>
    public async Task<bool> DeleteContextAsync(int id, int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Contexts
                .Where(x => x.Id == id)
                .ExecuteDeleteAsync();

            return response > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context silinirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return false;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    public async Task<List<Context>> CreateContextsAsync(List<ContextCreateDto> contextCreateDtos, int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var contexts = contextCreateDtos.Select(dto => new Context
            {
                Category = dto.Category,
                Title = dto.Title,
                Content = dto.Content,
                Embedding = dto.Embedding,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            await client.Contexts.AddRangeAsync(contexts);

            return contexts.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak oluşturulurken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, contextCreateDtos.Count);
            throw;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak günceller
    /// </summary>
    public async Task<List<Context>> UpdateContextsAsync(List<(int Id, ContextUpdateDto UpdateDto)> contextUpdates, int projectId)
    {
        try
        {
            var updatedContexts = new List<Context>();

            foreach (var (id, updateDto) in contextUpdates)
            {
                var updatedContext = await UpdateContextAsync(id, updateDto, projectId);
                if (updatedContext != null)
                {
                    updatedContexts.Add(updatedContext);
                }
            }

            return updatedContexts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak güncellenirken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, contextUpdates.Count);
            throw;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    public async Task<bool> DeleteContextsAsync(List<long> ids, int projectId)
    {
        try
        {
            var client = await _databaseService.GetProjectContext(projectId).Contexts
                .Where(x => ids.Contains(x.Id))
                .ExecuteDeleteAsync();


            return client > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak silinirken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, ids.Count);
            return false;
        }
    }

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    public async Task<ContextStatistics> GetContextStatisticsAsync(int projectId)
    {
        try
        {
            var client = _databaseService.GetProjectContext(projectId);
            var contexts = await client.Contexts.ToListAsync();

            var statistics = new ContextStatistics
            {
                TotalContexts = contexts.Count,
                ContextsWithEmbedding = contexts.Count(c => c.Embedding != null),
                CategoryDistribution = contexts
                    .GroupBy(c => c.Category)
                    .ToDictionary(g => g.Key, g => g.Count()),
                LastCreatedAt = contexts.Max(c => c.CreatedAt),
                LastUpdatedAt = contexts.Max(c => c.UpdatedAt)
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context istatistikleri getirilirken hata oluştu. Schema: {projectId}", projectId);
            throw;
        }
    }
}