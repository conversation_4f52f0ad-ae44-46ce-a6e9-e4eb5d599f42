using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Models;
using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Kimlik doğrulama işlemleri için API controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;
    private readonly IConfiguration _configuration;

    /// <summary>
    /// AuthController constructor
    /// </summary>
    /// <param name="authService">Authentication servis instance</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="configuration">Configuration instance</param>
    public AuthController(IAuthService authService, ILogger<AuthController> logger, IConfiguration configuration)
    {
        _authService = authService;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Kullanıcı girişi yapar
    /// </summary>
    /// <param name="request">Login isteği</param>
    /// <returns>Login sonucu</returns>
    [HttpPost("login")]
    public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new LoginResponse
                {
                    Success = false,
                    Message = "Geçersiz istek"
                });
            }

            var result = await _authService.LoginAsync(request.Password);
            
            if (result.Success && !string.IsNullOrEmpty(result.SessionId))
            {
                // Session ID'yi cookie olarak set et
                Response.Cookies.Append("SessionId", result.SessionId, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTimeOffset.UtcNow.AddMinutes(30)
                });
                
                return Ok(result);
            }
            
            return Unauthorized(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login endpoint'inde hata oluştu");
            return StatusCode(500, new LoginResponse
            {
                Success = false,
                Message = "Sunucu hatası"
            });
        }
    }

    /// <summary>
    /// Kullanıcı çıkışı yapar
    /// </summary>
    /// <returns>Logout sonucu</returns>
    [HttpPost("logout")]
    public async Task<ActionResult> Logout()
    {
        try
        {
            var sessionId = Request.Cookies["SessionId"];
            
            if (!string.IsNullOrEmpty(sessionId))
            {
                await _authService.LogoutAsync(sessionId);
            }
            
            // Cookie'yi temizle
            Response.Cookies.Delete("SessionId");
            
            return Ok(new { message = "Çıkış başarılı" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Logout endpoint'inde hata oluştu");
            return StatusCode(500, new { message = "Sunucu hatası" });
        }
    }

    /// <summary>
    /// Session durumunu kontrol eder
    /// </summary>
    /// <returns>Session durumu</returns>
    [HttpGet("status")]
    public async Task<ActionResult> GetStatus()
    {
        try
        {
            var sessionId = Request.Cookies["SessionId"];
            
            if (string.IsNullOrEmpty(sessionId))
            {
                return Ok(new { authenticated = false, message = "Session bulunamadı" });
            }
            
            var isValid = await _authService.ValidateSessionAsync(sessionId);
            
            if (isValid)
            {
                return Ok(new { authenticated = true, message = "Session geçerli" });
            }
            else
            {
                // Geçersiz session cookie'sini temizle
                Response.Cookies.Delete("SessionId");
                return Ok(new { authenticated = false, message = "Session süresi dolmuş" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Status endpoint'inde hata oluştu");
            return StatusCode(500, new { message = "Sunucu hatası" });
        }
    }
}