using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;


namespace TranslationAgentServer.Models;

/// <summary>
/// Supabase processes tablosuna karşılık gelen işlem modeli
/// Proje işlemlerini takip eder ve yönetir
/// </summary>

[Table("processes")]
public class Process
{
    /// <summary>
    /// İşlemin benzersiz UUID kimliği
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public Guid Id { get; set; }

    /// <summary>
    /// İşlemin oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Bağlı olduğu proje kimliği
    /// </summary>
    [Column("project_id")]
    public long ProjectId { get; set; }

    /// <summary>
    /// ilerleme
    /// </summary>
    [Column("progress")]
    public int Progress { get; set; }

    /// <summary>
    /// İşlem durumu (waiting, in_progress, completed, failed, paused, cancelled)
    /// </summary>
    [Column("status")]
    public ProcessStatus Status { get; set; } = ProcessStatus.Pending;

    /// <summary>
    /// İşlemin tamamlanma zamanı
    /// </summary>
    [Column("completed_at")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// İşlem sonucu
    /// </summary>
    [Column("result")]
    public string? Result { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    [Column("error_message")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// İşlem türü (translation, proofreading, import, export)
    /// </summary>
    [Column("task_type")]
    public ProcessTaskType TaskType { get; set; }

    /// <summary>
    /// Son iletişim zamanı
    /// </summary>
    [Column("last_ping")]
    public DateTime? LastPing { get; set; }
}

/// <summary>
/// İşlem oluşturma için kullanılan DTO modeli
/// </summary>
public class ProcessCreateDto
{
    /// <summary>
    /// Bağlı olduğu proje kimliği
    /// </summary>
    [Required(ErrorMessage = "Proje kimliği zorunludur")]
    public long ProjectId { get; set; }

    /// <summary>
    /// İşlem türü
    /// </summary>
    [Required(ErrorMessage = "İşlem türü zorunludur")]
    public ProcessTaskType TaskType { get; set; }

    /// <summary>
    /// İşlem durumu (varsayılan: pending)
    /// </summary>
    public ProcessStatus Status { get; set; } = ProcessStatus.Pending;
}

/// <summary>
/// İşlem güncelleme için kullanılan DTO modeli
/// </summary>
public class ProcessUpdateDto
{
    /// <summary>
    /// İşlem durumu
    /// </summary>
    public ProcessStatus? Status { get; set; }

    /// <summary>
    /// İşlem sonucu
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// Hata mesajı
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Son iletişim zamanı
    /// </summary>
    public DateTime? LastPing { get; set; }

    /// <summary>
    /// ilerleme
    /// </summary>
    public int? Progress { get; set; }
}



/// <summary>
/// İşlem durumu enum değerleri
/// </summary>
[JsonConverter(typeof(StringEnumConverter))]
public enum ProcessStatus
{
    /// <summary>
    /// Beklemede
    /// </summary>

    Pending,

    /// <summary>
    /// Devam ediyor
    /// </summary>
    InProgress,

    /// <summary>
    /// Tamamlandı
    /// </summary>
    Completed,

    /// <summary>
    /// Başarısız
    /// </summary>
    Failed,

    /// <summary>
    /// Duraklatıldı
    /// </summary>
    Paused,

    /// <summary>
    /// İptal edildi
    /// </summary>
    Cancelled
}

/// <summary>
/// İşlem türü enum değerleri
/// </summary>
public enum ProcessTaskType
{
    /// <summary>
    /// Proje oluşturma
    /// </summary>
    ProjectCreation,

    /// <summary>
    /// Metin çevirisi
    /// </summary>
    TextTranslation,

    /// <summary>
    /// Metin düzeltmesi
    /// </summary>
    TextProofreading,

    /// <summary>
    /// Terim tespiti
    /// </summary>
    TermsDetection,

    /// <summary>
    /// Terim çevirisi
    /// </summary>
    TermsTranslation
}
