using System.Text.Json;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// Proje yönetimi servisinin implementasyonu
/// Supabase ile proje CRUD operasyonlarını gerçekleştirir
/// </summary>
public class ProjectService : IProjectService
{
    private readonly ISupabaseService _supabaseService;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(ISupabaseService supabaseService, ILogger<ProjectService> logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Tüm projeleri listeler
    /// </summary>
    /// <returns>Proje listesi</returns>
    public async Task<IEnumerable<Project>> GetAllProjectsAsync()
    {
        try
        {

            _logger.LogInformation("Tüm projeler getiriliyor");

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Select("*")
                .Order("created_at", Supabase.Postgrest.Constants.Ordering.Descending)
                .Get();

            return response.Models ?? new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Projeler getirilirken hata oluştu");
            throw;
        }
    }

    /// <summary>
    /// Belirtilen ID'ye sahip projeyi getirir
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Proje bilgileri veya null</returns>
    public async Task<Project?> GetProjectByIdAsync(long id)
    {
        try
        {
            _logger.LogInformation("Proje getiriliyor: {ProjectId}", id);

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Select("*")
                .Where(x => x.Id == id)
                .Single();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje getirilirken hata oluştu: {ProjectId}", id);
            return null;
        }
    }

    /// <summary>
    /// Yeni proje oluşturur
    /// </summary>
    /// <param name="projectDto">Proje bilgileri</param>
    /// <returns>Oluşturulan proje</returns>
    public async Task<Project> CreateProjectAsync(ProjectDto projectDto)
    {
        try
        {
            _logger.LogInformation("Yeni proje oluşturuluyor: {ProjectName}", projectDto.Name);

            var project = new Project
            {
                Name = projectDto.Name,
                SpreadsheetId = projectDto.SpreadsheetId,
                TextsTable = projectDto.TextsTable,
                TermsTable = projectDto.TermsTable,
                SchemaName = projectDto.SchemaName,
                Settings = projectDto.Settings,
                CreatedAt = DateTime.UtcNow
            };

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Insert(project);

            var createdProject = response.Models?.FirstOrDefault();
            if (createdProject == null)
            {
                throw new InvalidOperationException("Proje oluşturulamadı");
            }
            _logger.LogInformation("Proje başarıyla oluşturuldu: {ProjectId}", createdProject.Id);
            return createdProject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje oluşturulurken hata oluştu: {ProjectName}", projectDto.Name);
            throw;
        }
    }

    /// <summary>
    /// Mevcut projeyi günceller
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <param name="projectUpdateDto">Güncellenecek proje bilgileri</param>
    /// <returns>Güncellenmiş proje veya null</returns>
    public async Task<Project?> UpdateProjectAsync(long id, ProjectUpdateDto projectUpdateDto)
    {
        try
        {
            _logger.LogInformation("Proje güncelleniyor: {ProjectId}", id);

            // Önce projenin var olup olmadığını kontrol et
            var existingProject = await GetProjectByIdAsync(id);
            if (existingProject == null)
            {
                _logger.LogWarning("Güncellenecek proje bulunamadı: {ProjectId}", id);
                return null;
            }

            // Güncelleme için yeni proje nesnesi oluştur
            var projectToUpdate = new Project
            {
                Id = id,
                Name = projectUpdateDto.Name ?? existingProject.Name,
                SpreadsheetId = projectUpdateDto.SpreadsheetId ?? existingProject.SpreadsheetId,
                TextsTable = projectUpdateDto.TextsTable ?? existingProject.TextsTable,
                TermsTable = projectUpdateDto.TermsTable ?? existingProject.TermsTable,
                Settings = projectUpdateDto.Settings ?? existingProject.Settings,
                CreatedAt = existingProject.CreatedAt
            };

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Where(x => x.Id == id)
                .Update(projectToUpdate);

            var updatedProject = response.Models?.FirstOrDefault();
            if (updatedProject != null)
            {
                _logger.LogInformation("Proje başarıyla güncellendi: {ProjectId}", id);
            }

            return updatedProject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje güncellenirken hata oluştu: {ProjectId}", id);
            throw;
        }
    }

    /// <summary>
    /// Projeyi siler
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> DeleteProjectAsync(long id)
    {
        try
        {
            _logger.LogInformation("Proje siliniyor: {ProjectId}", id);

            // Önce projenin var olup olmadığını kontrol et
            var existingProject = await GetProjectByIdAsync(id);
            if (existingProject == null)
            {
                _logger.LogWarning("Silinecek proje bulunamadı: {ProjectId}", id);
                return false;
            }

            await _supabaseService.GetClient()
                .From<Project>()
                .Where(x => x.Id == id)
                .Delete();

            _logger.LogInformation("Proje başarıyla silindi: {ProjectId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje silinirken hata oluştu: {ProjectId}", id);
            throw;
        }
    }

    /// <summary>
    /// Proje adına göre arama yapar
    /// </summary>
    /// <param name="name">Aranacak proje adı</param>
    /// <returns>Eşleşen projeler</returns>
    public async Task<IEnumerable<Project>> SearchProjectsByNameAsync(string name)
    {
        try
        {
            _logger.LogInformation("Proje aranıyor: {SearchTerm}", name);

            if (string.IsNullOrWhiteSpace(name))
            {
                return await GetAllProjectsAsync();
            }

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Select("*")
                .Where(x => x.Name!.Contains(name))
                .Order("created_at", Supabase.Postgrest.Constants.Ordering.Descending)
                .Get();

            return response.Models ?? new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje aranırken hata oluştu: {SearchTerm}", name);
            throw;
        }
    }

    /// <summary>
    /// Belirtilen Google Sheets ID'sine sahip projeyi bulur
    /// </summary>
    /// <param name="SpreadsheetId">Google Sheets ID</param>
    /// <returns>Proje bilgileri veya null</returns>
    public async Task<Project?> GetProjectBySpreadsheetIdAsync(string spreadsheetId)
    {
        try
        {
            _logger.LogInformation("Google Sheets ID ile proje aranıyor: {SpreadsheetId}", spreadsheetId);

            var response = await _supabaseService.GetClient()
                .From<Project>()
                .Select("*")
                .Where(x => x.SpreadsheetId == spreadsheetId)
                .Single();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Sheets ID ile proje aranırken hata oluştu: {SpreadsheetId}", spreadsheetId);
            return null;
        }
    }

    /// <summary>
    /// Projenin şemasını oluşturur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Şema oluşturma işleminin başarılı olup olmadığı</returns>
    public async Task<bool> CreateSchemaAsync(Project project)
    {
        try
        {

            _logger.LogInformation("Proje şeması oluşturuluyor: {ProjectId}", project.Id);

            var schemaName = $"project_{project.Id}";
            var schemaCreationResult = await _supabaseService.ExecuteDatabaseFunctionAsync<CreateSchemaResult>
            ("create_schema_if_not_exists", new { p_schema_name = schemaName });

            if (!schemaCreationResult.Success)
            {
                _logger.LogError("Proje şeması oluşturulurken hata oluştu: {Message}", schemaCreationResult.Message);
                return false;
            }
            else if (schemaCreationResult.Schema_Created)
            {
                _logger.LogInformation("Yeni şema başarıyla oluşturuldu: {SchemaName}", schemaName);
                if (schemaCreationResult.Tables_Created != null && schemaCreationResult.Tables_Created.Any())
                {
                    _logger.LogInformation("Oluşturulan tablolar: {Tables}", string.Join(", ", schemaCreationResult.Tables_Created));
                }
            }
            else
            {
                _logger.LogInformation("Şema zaten mevcut: {SchemaName}", schemaName);
            }

            var schemaAccess = await _supabaseService.ExecuteDatabaseFunctionAsync<SchemaAccessResult>
          ("add_schema_to_pgrst_config", new { new_schema = schemaName });

            if (schemaAccess.Success)
            {
                _logger.LogInformation("Şema erişimi başarıyla eklendi: {SchemaName}. Mesaj: {Message}", schemaName, schemaAccess.Message);
            }
            else
            {
                _logger.LogError("Şema erişimi eklenirken hata oluştu: {SchemaName}. Mesaj: {Message}", schemaName, schemaAccess.Message);
            }

            if (project != null)
            {
                project.SchemaName = schemaName;
                await _supabaseService.GetClient()
                .From<Project>()
                .Where(x => x.Id == project.Id)
                .Update(project);
                _logger.LogInformation("Proje şeması güncellendi: {SchemaName}", schemaName);
            }
            else
            {
                _logger.LogWarning("Project is null when trying to set schema name");
                return false;
            }



            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje şeması oluşturulurken hata oluştu: {ProjectId}", project.Id);
            return false;
        }
    }

}