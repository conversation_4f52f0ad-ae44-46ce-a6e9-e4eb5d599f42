using Catalyst;
using Mosaik.Core;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// Doğal dil işleme (NLP) operasyonları için servis arayüzü
    /// </summary>
    public interface INlpService
    {
        /// <summary>
        /// NLP pipeline'ını başlatır ve hazırlar
        /// </summary>
        /// <returns>Başlatma işleminin başarılı olup olmadığını belirten Task</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <returns>Lemmatized metin</returns>
        Task<string> ProcessTextToLemmaAsync(string text);

        /// <summary>
        /// Verilen metni analiz eder ve detaylı bilgi döndürür
        /// </summary>
        /// <param name="text"><PERSON><PERSON><PERSON> edile<PERSON>k metin</param>
        /// <returns>Metin analiz sonucu</returns>
        Task<TextAnalysisResult> AnalyzeTextAsync(string text);

        /// <summary>
        /// Pipeline'ın hazır olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Pipeline durumu</returns>
        bool IsReady { get; }
    }
}