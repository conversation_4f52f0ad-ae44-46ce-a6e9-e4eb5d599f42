using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Text servisi arayüzü
/// Metin verilerinin CRUD işlemlerini ve özel sorguları tanımlar
/// </summary>
public interface ITextService
{
    /// <summary>
    /// Tüm metinleri getirir (filtreleme seçenekleri ile)
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="page"><PERSON>fa numarası (1'den başlar)</param>
    /// <param name="pageSize">Say<PERSON> boyutu</param>
    /// <param name="namespaceFilter">Namespace filtresi (contains)</param>
    /// <param name="keyFilter">Key filtresi (contains)</param>
    /// <param name="enFilter">İngilizce metin filtresi (contains)</param>
    /// <param name="trFilter">Türkçe metin filtresi (contains)</param>
    /// <param name="statusFilter">Status filtresi (exact match)</param>
    /// <returns>Metin listesi</returns>
    Task<List<Text>> GetAllTextsAsync(long ProjectId, int page = 1, int pageSize = 50,
        string? namespaceFilter = null, string? keyFilter = null, string? enFilter = null,
        string? trFilter = null, TextStatus? statusFilter = null);

    /// <summary>
    /// ID'ye göre metin getirir
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Metin</returns>
    Task<Text?> GetTextByIdAsync(long id, long ProjectId);

    /// <summary>
    /// Anahtar kelimeye göre metinleri arar
    /// </summary>
    /// <param name="searchTerm">Arama terimi</param>
    /// <param name="schemaName">Şema adı</param>
    /// <param name="page">Sayfa numarası (1'den başlar)</param>
    /// <param name="pageSize">Sayfa boyutu</param>
    /// <returns>Metin listesi</returns>
    Task<List<Text>> SearchTextsAsync(string searchTerm, long ProjectId, int page = 1, int pageSize = 50);

    /// <summary>
    /// Embedding vektörüne göre benzer metinleri bulur
    /// </summary>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Benzer metin listesi</returns>
    Task<List<Text>> FindSimilarTextsAsync(float[] embedding, int limit, long ProjectId);

    /// <summary>
    /// Yeni metin oluşturur
    /// </summary>
    /// <param name="textCreateDto">Metin oluşturma DTO'su</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturulan metin</returns>
    Task<Text> CreateTextAsync(TextCreateDto textCreateDto, long ProjectId);

    /// <summary>
    /// Metin günceller
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="textUpdateDto">Metin güncelleme DTO'su</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Güncellenmiş metin</returns>
    Task<Text?> UpdateTextAsync(long id, TextUpdateDto textUpdateDto, long ProjectId);

    /// <summary>
    /// Metin siler
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteTextAsync(long id, long ProjectId);

    /// <summary>
    /// Metinleri toplu olarak oluşturur
    /// </summary>
    /// <param name="textCreateDtos">Metin oluşturma DTO listesi</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Oluşturulan metin listesi</returns>
    Task<bool> CreateTextsAsync(List<TextCreateDto> textCreateDtos, long ProjectId);

    /// <summary>
    /// Metinleri toplu olarak günceller
    /// </summary>
    /// <param name="textUpdates">Metin güncelleme listesi (ID ve DTO çiftleri)</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Güncellenmiş metin listesi</returns>
    Task<List<Text>> UpdateTextsAsync(List<(long Id, TextUpdateDto UpdateDto)> textUpdates, long ProjectId);

    /// <summary>
    /// Metinleri toplu olarak siler
    /// </summary>
    /// <param name="ids">Silinecek metin ID'leri</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteTextsAsync(List<long> ids, long ProjectId);


    /// <summary>
    /// Metin istatistiklerini getirir
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Metin istatistikleri</returns>
    Task<TextStatistics> GetTextStatisticsAsync(long ProjectId);
}

/// <summary>
/// Metin istatistikleri modeli
/// </summary>
public class TextStatistics
{
    /// <summary>
    /// Toplam metin sayısı
    /// </summary>
    public int TotalTexts { get; set; }

    /// <summary>
    /// İngilizce metin sayısı
    /// </summary>
    public int EnglishTexts { get; set; }

    /// <summary>
    /// Türkçe metin sayısı
    /// </summary>
    public int TurkishTexts { get; set; }

    /// <summary>
    /// Tekrar eden metin sayısı
    /// </summary>
    public int DuplicateTexts { get; set; }

    /// <summary>
    /// Boş metin sayısı
    /// </summary>
    public int NullTexts { get; set; }

    /// <summary>
    /// Embedding'i olan metin sayısı
    /// </summary>
    public int TextsWithEmbedding { get; set; }

    /// <summary>
    /// Lemmatize edilmiş metin sayısı
    /// </summary>
    public int LemmatizedTexts { get; set; }

    /// <summary>
    /// Namespace'lere göre dağılım
    /// </summary>
    public Dictionary<string, int> NamespaceDistribution { get; set; } = new();

    /// <summary>
    /// Sheet'lere göre dağılım
    /// </summary>
    public Dictionary<int, int> SheetDistribution { get; set; } = new();
}

