using System.ComponentModel.DataAnnotations;

namespace TranslationAgentServer.Models;

/// <summary>
/// Google Sheets API için service account bilgilerini içeren model
/// </summary>
public class GoogleServiceAccountRequest
{
    /// <summary>
    /// Service account JSON içeriği
    /// </summary>
    [Required]
    public string ServiceAccountJson { get; set; } = string.Empty;
}

/// <summary>
/// Spreadsheet bilgilerini içeren model
/// </summary>
public class SpreadsheetInfo
{
    /// <summary>
    /// Spreadsheet ID'si
    /// </summary>
    public string SpreadsheetId { get; set; } = string.Empty;
    
    /// <summary>
    /// Spreadsheet başlığı
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    public DateTime CreatedTime { get; set; }
    
    /// <summary>
    /// Son değiştirilme tarihi
    /// </summary>
    public DateTime ModifiedTime { get; set; }
    
    /// <summary>
    /// Spreadsheet URL'i
    /// </summary>
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Sheet (sayfa) bilgilerini içeren model
/// </summary>
public class SheetInfo
{
    /// <summary>
    /// Sheet ID'si
    /// </summary>
    public int SheetId { get; set; }
    
    /// <summary>
    /// Sheet başlığı
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// Sheet indeksi
    /// </summary>
    public int Index { get; set; }
    
    /// <summary>
    /// Satır sayısı
    /// </summary>
    public int RowCount { get; set; }
    
    /// <summary>
    /// Sütun sayısı
    /// </summary>
    public int ColumnCount { get; set; }
}

/// <summary>
/// Hücre verisi için model
/// </summary>
public class CellData
{
    /// <summary>
    /// Satır indeksi (0-based)
    /// </summary>
    public int Row { get; set; }
    
    /// <summary>
    /// Sütun indeksi (0-based)
    /// </summary>
    public int Column { get; set; }
    
    /// <summary>
    /// Hücre değeri
    /// </summary>
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// Satır ekleme/güncelleme için model
/// </summary>
public class SheetRowData
{
    /// <summary>
    /// Satır değerleri
    /// </summary>
    public List<string> Values { get; set; } = new();
}

/// <summary>
/// Toplu güncelleme için model
/// </summary>
public class BatchUpdateRequest
{
    /// <summary>
    /// Güncellenecek hücreler
    /// </summary>
    public List<CellUpdateData> Updates { get; set; } = new();
}

/// <summary>
/// Hücre güncelleme verisi
/// </summary>
public class CellUpdateData
{
    /// <summary>
    /// Hücre aralığı (örn: A1:B2)
    /// </summary>
    public string Range { get; set; } = string.Empty;
    
    /// <summary>
    /// Güncellenecek değerler
    /// </summary>
    public List<List<string>> Values { get; set; } = new();
}

/// <summary>
/// Aralık verisi için model
/// </summary>
public class RangeData
{
    /// <summary>
    /// Aralık (örn: A1:C10)
    /// </summary>
    public string Range { get; set; } = string.Empty;
    
    /// <summary>
    /// Değerler
    /// </summary>
    public List<List<string>> Values { get; set; } = new();
}

/// <summary>
/// API yanıt modeli
/// </summary>
public class GoogleSheetsApiResponse<T>
{
    /// <summary>
    /// İşlem başarılı mı
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// Yanıt verisi
    /// </summary>
    public T? Data { get; set; }
    
    /// <summary>
    /// Hata mesajı
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Sayfa oluşturma isteği
/// </summary>
public class CreateSheetRequest
{
    /// <summary>
    /// Sayfa başlığı
    /// </summary>
    [Required]
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// Satır sayısı (varsayılan: 1000)
    /// </summary>
    public int RowCount { get; set; } = 1000;
    
    /// <summary>
    /// Sütun sayısı (varsayılan: 26)
    /// </summary>
    public int ColumnCount { get; set; } = 26;
}

/// <summary>
/// İlk satır şablonu kontrol sonucu
/// </summary>
public class HeaderValidationResult
{
    /// <summary>
    /// Kontrol başarılı mı
    /// </summary>
    public bool IsValid { get; set; }
    
    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Beklenen başlık sütunları
    /// </summary>
    public List<string> ExpectedHeaders { get; set; } = new();
    
    /// <summary>
    /// Bulunan başlık sütunları
    /// </summary>
    public List<string> ActualHeaders { get; set; } = new();
    
    /// <summary>
    /// Eksik sütunlar
    /// </summary>
    public List<string> MissingHeaders { get; set; } = new();
    
    /// <summary>
    /// Fazla sütunlar
    /// </summary>
    public List<string> ExtraHeaders { get; set; } = new();
}

/// <summary>
/// İlk satır şablonu kontrol isteği
/// </summary>
public class ValidateHeaderRequest
{
    /// <summary>
    /// Spreadsheet ID'si
    /// </summary>
    [Required]
    public string SpreadsheetId { get; set; } = string.Empty;
    
    /// <summary>
    /// Sheet adı
    /// </summary>
    [Required]
    public string SheetName { get; set; } = string.Empty;
    
    /// <summary>
    /// Beklenen başlık şablonu (| ile ayrılmış)
    /// </summary>
    [Required]
    public string HeaderTemplate { get; set; } = string.Empty;
}