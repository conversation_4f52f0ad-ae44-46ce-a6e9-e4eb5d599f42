# Translation Agent Server

.NET 9.0 Web API projesi - Supabase veritabanı entegrasyonu ile Render.com için dockerize edilmiş

## 🚀 Render.com Deployment

Bu proje Render.com'da kolayca deploy edilebilir.

### Gereksinimler
- Git repository (GitHub, GitLab, Bitbucket)
- Render.com hesabı
- Supabase projesi ve service role key

### Deployment Adımları

1. **Repository'yi Render.com'a bağla**
   - Render.com dashboard'una git
   - "New" > "Web Service" seç
   - Git repository'ni bağla

2. **Konfigürasyon**
   - Runtime: `Docker` seç
   - Dockerfile path: `./Dockerfile`
   - Auto-deploy: Aktif

3. **Environment Variables**
   ```
   ASPNETCORE_ENVIRONMENT=Production
   ASPNETCORE_URLS=http://+:8080
   SUPABASE__URL=your_supabase_project_url
   SUPABASE__KEY=your_supabase_service_role_key
   ```

## 🗄️ Supabase Konfigürasyonu

### Supabase Projesi Oluşturma

1. [Supabase](https://supabase.com) hesabı oluşturun
2. Yeni proje oluşturun
3. Project Settings > API'den URL ve service_role key'ini alın

### Veritabanı Tablosu Oluşturma

Supabase SQL Editor'da aşağıdaki komutu çalıştırın:

```sql
CREATE TABLE translations (
    id SERIAL PRIMARY KEY,
    source_text TEXT NOT NULL,
    translated_text TEXT NOT NULL,
    source_language VARCHAR(10) NOT NULL,
    target_language VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Local Development

1. `appsettings.Development.json` dosyasını güncelleyin:
   ```json
   {
     "Supabase": {
       "Url": "YOUR_SUPABASE_URL_HERE",
       "Key": "YOUR_SUPABASE_SERVICE_ROLE_KEY_HERE"
     }
   }
   ```

2. Supabase bağımlılıklarını yükleyin:
   ```bash
   dotnet restore
   ```

### Render.com Environment Variables

Render.com dashboard'unda aşağıdaki environment variables'ları ekleyin:

```
SUPABASE__URL=your_supabase_project_url_here
SUPABASE__KEY=your_supabase_service_role_key_here
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://0.0.0.0:$PORT
```

### Dosya Yapısı

```
TranslationAgentServer/
├── Controllers/
│   ├── HealthController.cs      # Health check endpoint
│   └── WeatherForecastController.cs
├── Dockerfile                   # Docker konfigürasyonu
├── .dockerignore               # Docker ignore dosyası
├── render.yaml                 # Render.com konfigürasyonu
├── Program.cs                  # Ana uygulama dosyası
└── TranslationAgentServer.csproj
```

### Health Check

Uygulama aşağıdaki health check endpoint'lerini sağlar:

- `GET /health` - Basit sağlık kontrolü
- `GET /health/detailed` - Detaylı sistem bilgisi

### Docker Komutları

**Local olarak test etmek için:**

```bash
# Docker image oluştur
docker build -t translation-agent-server .

# Container çalıştır
docker run -p 8080:8080 translation-agent-server
```

### API Endpoints

#### Health Check
- `GET /health` - Sağlık kontrolü
- `GET /health/detailed` - Detaylı sağlık bilgisi

#### Translation API
- `GET /api/translation` - Tüm çevirileri listele
- `GET /api/translation/{id}` - Belirli bir çeviriyi getir
- `POST /api/translation` - Yeni çeviri oluştur
- `PUT /api/translation/{id}` - Çeviriyi güncelle
- `DELETE /api/translation/{id}` - Çeviriyi sil

#### Diğer
- `GET /weatherforecast` - Örnek weather endpoint

### Teknolojiler

- .NET 9.0
- ASP.NET Core Web API
- Docker
- Render.com

### Notlar

- Render.com ücretsiz planında 750 saat/ay kullanım limiti vardır
- Uygulama 15 dakika inaktivite sonrası uyku moduna geçer
- İlk istek sonrası uyanma süresi ~30 saniye olabilir

### Sorun Giderme

1. **Build hatası**: Dockerfile'ın doğru dizinde olduğundan emin olun
2. **Port hatası**: ASPNETCORE_URLS environment variable'ının doğru ayarlandığından emin olun
3. **Health check hatası**: `/health` endpoint'inin çalıştığını kontrol edin

### Destek

Sorularınız için issue açabilirsiniz.

### TODO LİST
- Embedding özelliği shared fonksiyon olarak sunulacak
- Text, term ve contextteki gereksiz fonksiyonlar ve endpointler temizlenecek