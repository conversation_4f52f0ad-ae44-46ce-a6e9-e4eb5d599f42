using Microsoft.EntityFrameworkCore;
using TranslationAgentServer.Data;

namespace TranslationAgentServer.Extensions;

/// <summary>
/// ApplicationDbContext için extension metodlar
/// </summary>
public static class DbContextExtensions
{
    /// <summary>
    /// Belirtilen şemadaki entity'leri sorgulamak için DbSet döndürür
    /// </summary>
    /// <typeparam name="TEntity">Entity tipi</typeparam>
    /// <param name="dbSet">DbSet</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>IQueryable<TEntity></returns>
    public static IQueryable<TEntity> Schema<TEntity>(this DbSet<TEntity> dbSet, string schemaName)
        where TEntity : class
    {
        return dbSet.FromSqlInterpolated($"SELECT * FROM \"{schemaName}\".\"{typeof(TEntity).Name.ToLower()}s\"");
    }

    /// <summary>
    /// ApplicationDbContext üzerinden direkt şema erişimi
    /// </summary>
    /// <typeparam name="TEntity">Entity tipi</typeparam>
    /// <param name="context">DbContext</param>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>IQueryable<TEntity></returns>
    public static IQueryable<TEntity> FromSchema<TEntity>(this ApplicationDbContext context, string schemaName)
        where TEntity : class
    {
        return context.Set<TEntity>().FromSqlInterpolated($"SELECT * FROM \"{schemaName}\".\"{typeof(TEntity).Name.ToLower()}s\"");
    }
}