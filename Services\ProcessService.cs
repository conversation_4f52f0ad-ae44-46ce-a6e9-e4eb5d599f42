using System.Collections.Concurrent;
using Microsoft.AspNetCore.Mvc.NewtonsoftJson;
using Supabase.Postgrest;
using Supabase.Postgrest.Converters;
using Supabase.Postgrest.Interfaces;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
namespace TranslationAgentServer.Services;

/// <summary>
/// Birleştirilmiş işlem yönetimi servisinin implementasyonu
/// Supabase ile işlem CRUD operasyonlarını, durum yönetimini ve arka plan görevlerini gerçekleştirir
/// </summary>
public class ProcessService : IProcessService
{
    private readonly ISupabaseService _supabaseService;
    private readonly IGoogleSheetsService _googleSheetsService;
    private readonly IProjectService _projectService;
    private readonly ITextService _textService;
    private readonly ITermService _termService;
    private readonly ILogger<ProcessService> _logger;
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _taskCancellationTokens;

    public ProcessService(
        ISupabaseService supabaseService,
        IGoogleSheetsService googleSheetsService,
        IProjectService projectService,
        ITextService textService,
        ITermService termService,
        ILogger<ProcessService> logger)
    {
        _supabaseService = supabaseService;
        _googleSheetsService = googleSheetsService;
        _projectService = projectService;
        _textService = textService;
        _termService = termService;
        _logger = logger;
        _taskCancellationTokens = new ConcurrentDictionary<Guid, CancellationTokenSource>();
    }


    #region CRUD Operations

    /// <summary>
    /// Belirtilen projeye ait tüm işlemleri listeler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>İşlem listesi</returns>
    public async Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(long projectId)
    {
        try
        {
            _logger.LogInformation("Proje işlemleri getiriliyor: {ProjectId}", projectId);

            var response = await _supabaseService.GetClient()
                .From<Process>()
                .Select("*")
                .Where(x => x.ProjectId == projectId)
                .Order("created_at", Supabase.Postgrest.Constants.Ordering.Descending)
                .Get();

            return response.Models ?? new List<Process>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje işlemleri getirilirken hata oluştu: {ProjectId}", projectId);
            throw;
        }
    }



    /// <summary>
    /// Belirtilen ID'ye sahip işlemi getirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İşlem bilgileri veya null</returns>
    public async Task<Process?> GetProcessByIdAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem getiriliyor: {ProcessId}", id);

            var response = await _supabaseService.GetClient()
                .From<Process>()
                .Select("*")
                .Where(x => x.Id == id)
                .Single();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem getirilirken hata oluştu: {ProcessId}", id);
            return null;
        }
    }

    /// <summary>
    /// Yeni işlem oluşturur ve başlatır
    /// </summary>
    /// <param name="processDto">İşlem bilgileri</param>
    /// <returns>Oluşturulan ve başlatılan işlem</returns>
    public async Task<Process> CreateAndStartProcessAsync(ProcessCreateDto processDto)
    {
        try
        {

            // çalışan process kontrolü (proje için)
            var check = await _supabaseService.GetClient()
        .From<Process>()
        .Where(p => p.ProjectId == processDto.ProjectId)
        .Or(new List<IPostgrestQueryFilter>{
    new QueryFilter("status",Constants.Operator.Equals, nameof(ProcessStatus.InProgress)),
    new QueryFilter("status", Constants.Operator.Equals, nameof(ProcessStatus.Pending)),
    new QueryFilter("status", Constants.Operator.Equals, nameof(ProcessStatus.Paused))
        }).Single();

            if (check != null)
            {
                throw new InvalidOperationException("Projenin zaten bir işlemi var. Önceki işlemi tamamlamadan veya durdurmadan yeni bir işlem oluşturamazsınız.");
            }


            _logger.LogInformation("Yeni işlem oluşturuluyor: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);

            var process = new Process
            {
                Id = Guid.NewGuid(),
                ProjectId = processDto.ProjectId,
                TaskType = processDto.TaskType.ToString(),
                Status = ProcessStatus.Pending.ToString(),
                CreatedAt = DateTime.UtcNow,
                LastPing = DateTime.UtcNow,
                Progress = 0,
                Result = null,
                ErrorMessage = null
            };

            var response = await _supabaseService.GetClient()
                .From<Process>()
                .Insert(process);

            var createdProcess = response.Models?.FirstOrDefault();
            if (createdProcess == null)
            {
                throw new InvalidOperationException("İşlem oluşturulamadı");
            }

            _logger.LogInformation("İşlem başarıyla oluşturuldu: {ProcessId}", createdProcess.Id);


            _logger.LogInformation("Process task başlatılıyor: {ProcessId}, Type: {TaskType}", createdProcess.Id, createdProcess.TaskType);

            // Cancellation token oluştur
            var cts = new CancellationTokenSource();
            _taskCancellationTokens[createdProcess.Id] = cts;

            // Task'ı arka planda başlat
            _ = Task.Run(async () => await ExecuteProcessTaskAsync(createdProcess, cts.Token), cts.Token);

            var updateDto = new ProcessUpdateDto
            {
                Status = ProcessStatus.InProgress.ToString(),
                LastPing = DateTime.UtcNow,
            };
            await UpdateProcessAsync(createdProcess.Id, updateDto);

            _logger.LogInformation("Process task başarıyla başlatıldı: {ProcessId}", createdProcess.Id);
            return createdProcess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem oluşturulurken hata oluştu: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);
            throw;
        }
    }

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <param name="processUpdateDto">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem veya null</returns>
    public async Task<Process?> UpdateProcessAsync(Guid id, ProcessUpdateDto processUpdateDto)
    {
        try
        {
            _logger.LogInformation("İşlem güncelleniyor: {ProcessId}", id);

            // Önce işlemin var olup olmadığını kontrol et
            var existingProcess = await GetProcessByIdAsync(id);
            if (existingProcess == null)
            {
                _logger.LogWarning("Güncellenecek işlem bulunamadı: {ProcessId}", id);
                return null;
            }

            // Güncelleme için yeni işlem nesnesi oluştur
            var processToUpdate = new Process
            {
                Id = id,
                ProjectId = existingProcess.ProjectId,
                TaskType = existingProcess.TaskType,
                Status = processUpdateDto.Status != null ? processUpdateDto.Status.ToString() : existingProcess.Status,
                CreatedAt = existingProcess.CreatedAt,
                CompletedAt = processUpdateDto.Status == ProcessStatus.Completed.ToString() || processUpdateDto.Status == ProcessStatus.Failed.ToString() || processUpdateDto.Status == ProcessStatus.Cancelled.ToString()
                    ? DateTime.UtcNow : existingProcess.CompletedAt,
                Result = processUpdateDto.Result ?? existingProcess.Result,
                Progress = processUpdateDto.Progress ?? existingProcess.Progress,
                ErrorMessage = processUpdateDto.ErrorMessage ?? existingProcess.ErrorMessage,
                LastPing = processUpdateDto.LastPing ?? DateTime.UtcNow
            };

            var response = await _supabaseService.GetClient()
                .From<Process>()
                .Where(x => x.Id == id)
                .Update(processToUpdate);

            var updatedProcess = response.Models?.FirstOrDefault();
            if (updatedProcess != null)
            {
                _logger.LogInformation("İşlem başarıyla güncellendi: {ProcessId}", id);
            }

            return updatedProcess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem güncellenirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemi siler
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> DeleteProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem siliniyor: {ProcessId}", id);

            // Önce işlemin var olup olmadığını kontrol et
            var existingProcess = await GetProcessByIdAsync(id);
            if (existingProcess == null)
            {
                _logger.LogWarning("Silinecek işlem bulunamadı: {ProcessId}", id);
                return false;
            }

            if (existingProcess.Status == ProcessStatus.InProgress.ToString() || existingProcess.Status == ProcessStatus.Paused.ToString())
            {
                var cancel = await CancelProcessAsync(id);
                if (!cancel)
                {
                    _logger.LogWarning("İşlem durdurulamadı: {ProcessId}", id);
                    return false;
                }
            }

            await _supabaseService.GetClient()
                .From<Process>()
                .Where(x => x.Id == id)
                .Delete();

            _logger.LogInformation("İşlem başarıyla silindi: {ProcessId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem silinirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }


    /// <summary>
    /// İşlemi duraklatır
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Duraklama işleminin başarılı olup olmadığı</returns>
    public async Task<bool> PauseProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem duraklatılıyor: {ProcessId}", id);

            // Durumu güncelle
            var updateDto = new ProcessUpdateDto
            {
                Status = ProcessStatus.Paused.ToString(),
                LastPing = DateTime.UtcNow
            };
            await UpdateProcessAsync(id, updateDto);

            _logger.LogInformation("İşlem başarıyla duraklatıldı: {ProcessId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem duraklatılırken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemi devam ettirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Devam ettirme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> ResumeProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem devam ettiriliyor: {ProcessId}", id);


            _logger.LogInformation("İşlem başarıyla devam ettirildi: {ProcessId}", id);

            // Durumu güncelle
            var updateDto = new ProcessUpdateDto
            {
                Status = ProcessStatus.InProgress.ToString(),
                LastPing = DateTime.UtcNow
            };
            await UpdateProcessAsync(id, updateDto);

            _logger.LogInformation("İşlem başarıyla devam ettirildi: {ProcessId}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem devam ettirilirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemi iptal eder
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İptal işleminin başarılı olup olmadığı</returns>
    public async Task<bool> CancelProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem iptal ediliyor: {ProcessId}", id);
            if (_taskCancellationTokens.TryGetValue(id, out var cts))
            {
                cts.Cancel();

                // Durumu güncelle
                var updateDto = new ProcessUpdateDto
                {
                    Status = ProcessStatus.Cancelled.ToString(),
                    Result = "İşlem kullanıcı tarafından iptal edildi",
                    LastPing = DateTime.UtcNow
                };
                await UpdateProcessAsync(id, updateDto);

                _logger.LogInformation("Process task başarıyla iptal edildi: {ProcessId}", id);
                return true;
            }
            else
            {
                _logger.LogWarning("İşlem iptal edilemedi: {ProcessId}", id);
                return false;
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem iptal edilirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemin son iletişim zamanını günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Güncelleme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> UpdateLastPingAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem ping güncelleniyor: {ProcessId}", id);

            var updateDto = new ProcessUpdateDto
            {
                LastPing = DateTime.UtcNow
            };

            var updatedProcess = await UpdateProcessAsync(id, updateDto);
            return updatedProcess != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem ping güncellenirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }


    #endregion

    #region Private Methods



    /// <summary>
    /// Process task'ını çalıştırır
    /// </summary>
    /// <param name="taskInfo">Task bilgileri</param>
    /// <param name="cancellationToken">İptal token'ı</param>
    /// <returns>Task</returns>
    private async Task ExecuteProcessTaskAsync(Process process, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Process task çalıştırılıyor: {ProcessId}, Type: {TaskType}",
                process.Id, process.TaskType);

            // Process durumunu güncelle
            var updateDto_first = new ProcessUpdateDto
            {
                Status = ProcessStatus.InProgress.ToString(),
                LastPing = process.LastPing,
                Progress = 0,
                Result = "İşlem başlatılıyor..."
            };
            await UpdateProcessAsync(process.Id, updateDto_first);

            // Task türüne göre işlem yap
            switch (process.TaskType)
            {
                case nameof(ProcessTaskType.ProjectCreation):
                    await ExecuteProjectCreationTask(process, cancellationToken);
                    break;
                case nameof(ProcessTaskType.TextTranslation):
                    await ExecuteTextTranslationTask(process, cancellationToken);
                    break;
                case nameof(ProcessTaskType.TextProofreading):
                    await ExecuteTextProofreadingTask(process, cancellationToken);
                    break;
                case nameof(ProcessTaskType.TermsDetection):
                    await ExecuteTermsDetectionTask(process, cancellationToken);
                    break;
                case nameof(ProcessTaskType.TermsTranslation):
                    await ExecuteTermsTranslationTask(process, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"Desteklenmeyen task türü: {process.TaskType}");
            }

            // Başarıyla tamamlandı
            process.Status = ProcessStatus.Completed.ToString();
            process.LastPing = DateTime.UtcNow;
            process.Progress = 100;

            // Process durumunu güncelle
            await UpdateProcess(process);

            _logger.LogInformation("Process task başarıyla tamamlandı: {ProcessId}", process.Id);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Process task iptal edildi: {ProcessId}", process.Id);
            process.Status = ProcessStatus.Cancelled.ToString();
            process.LastPing = DateTime.UtcNow;

            // Process durumunu güncelle
            await UpdateProcess(process);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Process task çalıştırılırken hata oluştu: {ProcessId}", process.Id);

            process.Status = ProcessStatus.Failed.ToString();
            process.LastPing = DateTime.UtcNow;
            process.ErrorMessage = ex.Message;
            process.Result = "İşlem başarısız oldu";

            // Process durumunu güncelle
            try
            {
                await UpdateProcess(process);
            }
            catch (Exception updateEx)
            {
                _logger.LogError(updateEx, "Process durumu güncellenirken hata oluştu: {ProcessId}", process.Id);
            }
        }
    }

    /// <summary>
    /// Proje oluşturma task'ını çalıştırır
    /// </summary>
    private async Task ExecuteProjectCreationTask(Process process, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Proje oluşturma task'ı çalıştırılıyor: {ProcessId}", process.Id);

        try
        {
            await ProcessTextsFromGoogleSheets(process, cancellationToken);
            await ProcessTermsFromGoogleSheets(process, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Metin çevirisi task'ı iptal edildi: {ProcessId}", process.Id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin çevirisi task'ı çalıştırılırken hata oluştu: {ProcessId}", process.Id);
            throw;
        }
    }

    /// <summary>
    /// Google Sheets'ten metinleri işler ve veritabanına kaydeder
    /// </summary>
    private async Task ProcessTextsFromGoogleSheets(Process process, CancellationToken cancellationToken)
    {

        // Proje bilgilerini al
        process.Result = "Proje bilgileri alınıyor...";
        process.Progress = 5;
        await UpdateProcess(process);

        var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
        if (project == null)
        {
            throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");
        }

        if (string.IsNullOrEmpty(project.SpreadsheetId) || string.IsNullOrEmpty(project.TextsTable))
        {
            throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId veya TextsTable)");
        }

        // Google Sheets servisinin başlatılıp başlatılmadığını kontrol et
        process.Result = "Google Sheets servisi kontrol ediliyor...";
        process.Progress = 10;
        await UpdateProcess(process);
        if (!_googleSheetsService.IsServiceInitialized())
        {
            throw new InvalidOperationException("Google Sheets servisi başlatılmamış");
        }

        process.Result = "Google Sheets'ten veriler çekiliyor...";
        process.Progress = 20;
        await UpdateProcess(process);
        // Google Sheets'ten verileri çek
        var sheetDataResponse = await _googleSheetsService.GetSheetDataAsync(project.SpreadsheetId, project.TextsTable);

        if (!sheetDataResponse.Success || sheetDataResponse.Data == null)
        {
            throw new InvalidOperationException($"Google Sheets'ten veri çekilemedi: {sheetDataResponse.ErrorMessage}");
        }

        var sheetData = sheetDataResponse.Data;
        if (sheetData.Count == 0)
        {
            throw new InvalidOperationException("Google Sheets'te veri bulunamadı");
        }

        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }



        // İlk satırı başlık olarak kabul et ve atla
        var dataRows = sheetData.Skip(1).ToList();
        if (dataRows.Count == 0)
        {
            throw new InvalidOperationException("Google Sheets'te başlık dışında veri bulunamadı");
        }
        process.Result = $"{dataRows.Count} veri satırı işlenecek";
        process.Progress = 30;
        await UpdateProcess(process);


        // Sütun konfigürasyonunu al
        var textsColumns = await _supabaseService.GetMainValueAsync("texts_columns"); // row_id|namespace|key|en|tr|status
        var expectedHeaders = _googleSheetsService.ParseHeaderTemplate(textsColumns);



        // Verileri TextCreateDto listesine dönüştür
        var textCreateDtos = new List<TextCreateDto>();
        var processedCount = 0;
        var totalRows = dataRows.Count;

        for (int i = 0; i < dataRows.Count; i++)
        {


            var row = dataRows[i];
            var status = TextStatus.EN;
            var textStatus = _googleSheetsService.GetColumnValue(row, expectedHeaders, "status");
            if (textStatus.Equals("TR", StringComparison.OrdinalIgnoreCase))
            {
                status = TextStatus.TR;
            }
            else if (textStatus.Equals("DUPE", StringComparison.OrdinalIgnoreCase))
            {
                status = TextStatus.DUPE;
            }
            else if (textStatus.Equals("NULL", StringComparison.OrdinalIgnoreCase))
            {
                status = TextStatus.NULL;
            }

            var textCreateDto = new TextCreateDto
            {
                RowID = int.TryParse(_googleSheetsService.GetColumnValue(row, expectedHeaders, "row_id"), out var rowId) ? rowId : -1,
                Namespace = _googleSheetsService.GetColumnValue(row, expectedHeaders, "namespace"),
                Key = _googleSheetsService.GetColumnValue(row, expectedHeaders, "key"),
                En = _googleSheetsService.GetColumnValue(row, expectedHeaders, "en"),
                Tr = _googleSheetsService.GetColumnValue(row, expectedHeaders, "tr"),
                Status = status
            };

            // Temel validasyon
            if (!string.IsNullOrWhiteSpace(textCreateDto.En) && textCreateDto.RowID > 0)
            {
                textCreateDtos.Add(textCreateDto);
            }

            processedCount++;

            // Her 100 satırda bir ilerleme güncelle
            if (processedCount % 1000 == 0 || processedCount == totalRows)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Duraklatma kontrolü
                if (process.Status == ProcessStatus.Paused.ToString())
                {
                    while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                }

                var progressPercent = 30 + (int)((double)processedCount / totalRows * 20); // 30-50 arası
                process.Progress = progressPercent;
                process.Result = $"{processedCount}/{totalRows} satır işlendi";
                await UpdateProcess(process);
            }
        }

        if (textCreateDtos.Count == 0)
        {
            process.ErrorMessage = "İşlenebilir veri bulunamadı";
            process.Status = ProcessStatus.Failed.ToString();
            await UpdateProcess(process);
            throw new InvalidOperationException("İşlenebilir veri bulunamadı");
        }

        process.Progress = 50;
        process.Result = $"{textCreateDtos.Count} geçerli metin kaydı hazırlandı";
        await UpdateProcess(process);

        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }

        process.Progress = 60;
        process.Result = "Veritabanına metinler kaydediliyor...";
        await UpdateProcess(process);

        try
        {
            // Toplu metin oluşturma işlemi
            var createResult = await _textService.CreateTextsAsync(textCreateDtos, process.ProjectId);

            if (!createResult)
            {
                throw new InvalidOperationException("Metinler veritabanına kaydedilemedi");
            }
        }
        catch
        {
            throw;
        }


        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }
        process.Progress = 70;
        process.Result = "Metinler başarıyla kaydedildi";
        await UpdateProcess(process);


        // İstatistikleri al
        var statistics = await _textService.GetTextStatisticsAsync(process.ProjectId);
        var resultMessage = $"İşlem tamamlandı. Toplam {textCreateDtos.Count} metin kaydedildi. " +
                          $"Veritabanında toplam {statistics.TotalTexts} metin bulunuyor.";

        process.Progress = 80;
        process.Result = resultMessage;
        await UpdateProcess(process);

        _logger.LogInformation("Text create task'ı başarıyla tamamlandı: {ProcessId} - {Count} metin işlendi",
            process.Id, textCreateDtos.Count);
    }


    /// <summary>
    /// Google Sheets'ten terimleri işler ve veritabanına kaydeder
    /// </summary>
    private async Task ProcessTermsFromGoogleSheets(Process process, CancellationToken cancellationToken)
    {

        // Proje bilgilerini al
        process.Result = "Proje bilgileri alınıyor...";
        process.Progress = 80;
        await UpdateProcess(process);

        var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
        if (project == null)
        {
            throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");
        }

        if (string.IsNullOrEmpty(project.SpreadsheetId) || string.IsNullOrEmpty(project.TermsTable))
        {
            throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId veya TermsTable)");
        }


        if (!_googleSheetsService.IsServiceInitialized())
        {
            throw new InvalidOperationException("Google Sheets servisi başlatılmamış");
        }

        // Google Sheets'ten verileri çek
        process.Progress = 85;
        process.Result = "Google Sheets'ten veriler çekiliyor...";
        var sheetDataResponse = await _googleSheetsService.GetSheetDataAsync(project.SpreadsheetId, project.TermsTable);

        if (!sheetDataResponse.Success || sheetDataResponse.Data == null)
        {
            throw new InvalidOperationException($"Google Sheets'ten veri çekilemedi: {sheetDataResponse.ErrorMessage}");
        }

        var sheetData = sheetDataResponse.Data;
        if (sheetData.Count == 0)
        {

            throw new InvalidOperationException("Google Sheets'te veri bulunamadı");
        }

        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }

        // İlk satırı başlık olarak kabul et ve atla
        var dataRows = sheetData.Skip(1).ToList();
        if (dataRows.Count == 0)
        {
            _logger.LogInformation("Google Sheets'te terimcede başlık dışında veri bulunamadı");
            process.Result = "Google Sheets'te terimcede başlık dışında veri bulunamadı";
            process.Progress = 100;
            process.Status = ProcessStatus.Completed.ToString();
            await UpdateProcess(process);
            return;
        }

        process.Progress = 90;
        process.Result = $"{dataRows.Count} veri satırı işlenecek";
        await UpdateProcess(process);

        // Sütun konfigürasyonunu al
        var termsColumns = await _supabaseService.GetMainValueAsync("terms_columns"); // row_id|en|tr|category|info|status
        var expectedHeaders = _googleSheetsService.ParseHeaderTemplate(termsColumns);



        // Verileri TermCreateDto listesine dönüştür
        var termCreateDtos = new List<TermCreateDto>();
        var processedCount = 0;
        var totalRows = dataRows.Count;

        for (int i = 0; i < dataRows.Count; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // Duraklatma kontrolü
            if (process.Status == ProcessStatus.Paused.ToString())
            {
                while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            var row = dataRows[i];
            var status = TermStatus.EN;
            var termStatus = _googleSheetsService.GetColumnValue(row, expectedHeaders, "status");
            if (termStatus.Equals("TR", StringComparison.OrdinalIgnoreCase))
            {
                status = TermStatus.TR;
            }


            var termCreateDto = new TermCreateDto
            {
                RowId = int.TryParse(_googleSheetsService.GetColumnValue(row, expectedHeaders, "row_id"), out var rowId) ? rowId : -1,
                En = _googleSheetsService.GetColumnValue(row, expectedHeaders, "en"),
                Tr = _googleSheetsService.GetColumnValue(row, expectedHeaders, "tr"),
                Category = _googleSheetsService.GetColumnValue(row, expectedHeaders, "category"),
                Info = _googleSheetsService.GetColumnValue(row, expectedHeaders, "info"),
                Status = status
            };

            // Temel validasyon
            if (!string.IsNullOrWhiteSpace(termCreateDto.En) && termCreateDto.RowId > 0)
            {
                termCreateDtos.Add(termCreateDto);
            }

            processedCount++;

            // Her 100 satırda bir ilerleme güncelle
            if (processedCount % 100 == 0 || processedCount == totalRows)
            {
                var progressPercent = 90 + (int)((double)processedCount / totalRows * 5); // 90-95 arası
                process.Progress = progressPercent;
                process.Result = $"{processedCount}/{totalRows} satır işlendi";
                await UpdateProcess(process);
            }
        }

        if (termCreateDtos.Count == 0)
        {
            throw new InvalidOperationException("İşlenebilir veri bulunamadı");
        }

        process.Progress = 98;
        process.Result = $"{termCreateDtos.Count} geçerli terim kaydı hazırlandı";
        await UpdateProcess(process);

        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }
        process.Progress = 99;
        process.Result = "Veritabanına terimler kaydediliyor...";
        await UpdateProcess(process);

        // Toplu terim oluşturma işlemi

        try
        {
            // Toplu metin oluşturma işlemi
            var createResult = await _termService.CreateTermsAsync(termCreateDtos, process.ProjectId);

            if (!createResult)
            {
                throw new InvalidOperationException("Terimler veritabanına kaydedilemedi");
            }
        }
        catch
        {
            throw;
        }


        cancellationToken.ThrowIfCancellationRequested();
        // Duraklatma kontrolü
        if (process.Status == ProcessStatus.Paused.ToString())
        {
            while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }


        // İstatistikleri al
        var statistics = await _termService.GetTermStatisticsAsync(process.ProjectId);
        var resultMessage = $"İşlem tamamlandı. Toplam {termCreateDtos.Count} terim kaydedildi. " +
                          $"Veritabanında toplam {statistics.TotalTerms} terim bulunuyor.";

        process.Progress = 100;
        process.Result = resultMessage;
        process.Status = ProcessStatus.Completed.ToString();
        await UpdateProcess(process);

        _logger.LogInformation("Term create task'ı başarıyla tamamlandı: {ProcessId} - {Count} terim işlendi",
            process.Id, termCreateDtos.Count);
    }

    /// <summary>
    /// Metin çevirisi task'ını çalıştırır
    /// Google Sheets'ten verileri çeker ve Text servisi ile toplu metin oluşturur
    /// </summary>
    private async Task ExecuteTextTranslationTask(Process process, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Metin çevirisi task'ı çalıştırılıyor: {ProcessId}", process.Id);

        // İlerleme güncelleme helper fonksiyonu
        async Task UpdateProcess(int progress, string message = "")
        {
            process.Progress = progress;
            var updateDto = new ProcessUpdateDto
            {
                Progress = progress,
                LastPing = DateTime.UtcNow,
                Result = message
            };
            await UpdateProcessAsync(process.Id, updateDto);
            _logger.LogInformation("İlerleme güncellendi: {ProcessId} - %{Progress} - {Message}", process.Id, progress, message);
        }
    }


    /// <summary>
    /// Metin düzeltmesi task'ını çalıştırır
    /// </summary>
    private async Task ExecuteTextProofreadingTask(Process process, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Metin düzeltmesi task'ı çalıştırılıyor: {ProcessId}", process.Id);

        // Simülasyon için bekleme
        for (int i = 0; i <= 100; i += 8)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (process.Status == ProcessStatus.Paused.ToString())
            {
                while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            process.Progress = i;
            await Task.Delay(1500, cancellationToken);
        }
    }

    /// <summary>
    /// Terim tespiti task'ını çalıştırır
    /// </summary>
    private async Task ExecuteTermsDetectionTask(Process process, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Terim tespiti task'ı çalıştırılıyor: {ProcessId}", process.Id);

        // Simülasyon için bekleme
        for (int i = 0; i <= 100; i += 12)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (process.Status == ProcessStatus.Paused.ToString())
            {
                while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            process.Progress = i;
            await Task.Delay(1200, cancellationToken);
        }
    }

    /// <summary>
    /// Terim çevirisi task'ını çalıştırır
    /// </summary>
    private async Task ExecuteTermsTranslationTask(Process process, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Terim çevirisi task'ı çalıştırılıyor: {ProcessId}", process.Id);

        // Simülasyon için bekleme
        for (int i = 0; i <= 100; i += 15)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (process.Status == ProcessStatus.Paused.ToString())
            {
                while (process.Status == ProcessStatus.Paused.ToString() && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            process.Progress = i;
            await Task.Delay(1000, cancellationToken);
        }
    }

    // İlerleme güncelleme helper fonksiyonu
    private async Task UpdateProcess(Process process)
    {

        var updateDto = new ProcessUpdateDto
        {
            Progress = process.Progress,
            LastPing = DateTime.UtcNow,
            Result = process.Result,
            Status = process.Status,
            ErrorMessage = process.ErrorMessage
        };
        await UpdateProcessAsync(process.Id, updateDto);
        _logger.LogInformation("İşlem güncellendi: {ProcessId} - {Status} - {Message}", process.Id, process.Status, process.Result);
    }

    #endregion
}