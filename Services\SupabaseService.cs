using System.Text.Json;
using Microsoft.Extensions.Options;
using Supabase;
using TranslationAgentServer.Configurations;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// Supabase veritabanı işlemleri için servis implementasyonu
/// </summary>
public class SupabaseService : ISupabaseService
{
    private readonly Client _client;

    private Client? _projectClient;

    private long _currentProjectId = 0;

    private readonly SupabaseConfig _supabaseOptions;

    private readonly ILogger<SupabaseService> _logger;
    private bool _isInitialized = false;
    private bool _isProjectInitialized = false;
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
    };

    /// <summary>
    /// SupabaseService constructor
    /// </summary>
    /// <param name="options">Supabase yapılandırma seçenekleri</param>
    /// <param name="logger">Logger instance</param>
    public SupabaseService(IOptions<SupabaseConfig> options, ILogger<SupabaseService> logger)
    {
        _logger = logger;

        _supabaseOptions = options.Value;

        if (string.IsNullOrEmpty(_supabaseOptions.Url) || string.IsNullOrEmpty(_supabaseOptions.Key))
        {
            throw new ArgumentException("Supabase URL ve Key değerleri boş olamaz.");
        }

        var clientOptions = new SupabaseOptions
        {
            AutoConnectRealtime = false, // Backend için realtime bağlantısı genellikle gerekli değil
            Schema = "public"
        };

        _client = new Client(_supabaseOptions.Url, _supabaseOptions.Key, clientOptions);

        _logger.LogInformation("Supabase client oluşturuldu.");
    }

    /// <summary>
    /// Supabase proje clienti oluştur
    /// </summary>
    /// <param name="ProjectId">Proje kimliği</param>
    /// <returns>Yapılandırılmış Supabase client</returns>
    public async Task<Client> SetProjectClient(long ProjectId)
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }

        _currentProjectId = ProjectId;
        var clientOptions = new SupabaseOptions
        {
            AutoConnectRealtime = false, // Backend için realtime bağlantısı genellikle gerekli değil
            Schema = $"project_{ProjectId}",

        };

        _projectClient = new Client(_supabaseOptions.Url, _supabaseOptions.Key, clientOptions);
        _logger.LogInformation("Supabase proje clienti oluşturuldu.");
        await _projectClient.InitializeAsync();
        _isProjectInitialized = true;
        _logger.LogInformation("Supabase proje clienti başlatıldı.");
        return _projectClient;
    }

    /// <summary>
    /// Supabase proje client'ını döndürür
    /// </summary>
    /// <param name="ProjectId">Proje kimliği</param>
    /// <returns>Yapılandırılmış Supabase client</returns>
    public async Task<Client> GetProjectClient(long ProjectId)
    {

        if (_projectClient == null || !_isProjectInitialized || _currentProjectId != ProjectId)
        {
            _projectClient = await SetProjectClient(ProjectId);
        }
        return _projectClient!;
    }


    /// <summary>
    /// Supabase client örneğini döndürür
    /// </summary>
    /// <returns>Yapılandırılmış Supabase client</returns>
    public Client GetClient()
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }
        return _client;
    }

    /// <summary>
    /// Supabase bağlantısını başlatır
    /// </summary>
    /// <returns>Asenkron işlem</returns>
    public async Task InitializeAsync()
    {
        try
        {
            await _client.InitializeAsync();
            _isInitialized = true;
            _logger.LogInformation("Supabase client başarıyla başlatıldı.");

            var schemaAccess = await ExecuteDatabaseFunctionAsync<SchemaAccessResult>
               ("add_schema_to_pgrst_config", new { new_schema = "project_28" });

            if (schemaAccess.Success)
            {
                _logger.LogInformation("Şema erişimi başarıyla eklendi: {SchemaName}. Mesaj: {Message}", "project_28", schemaAccess.Message);
            }
            else
            {
                _logger.LogError("Şema erişimi eklenirken hata oluştu: {SchemaName}. Mesaj: {Message}", "project_28", schemaAccess.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Supabase client başlatılırken hata oluştu.");
            throw;
        }
    }

    /// <summary>
    /// Belirtilen tablodan veri çeker
    /// </summary>
    /// <typeparam name="T">Model tipi</typeparam>
    /// <returns>Tablo referansı</returns>
    public Supabase.Interfaces.ISupabaseTable<T, Supabase.Realtime.RealtimeChannel> From<T>() where T : Supabase.Postgrest.Models.BaseModel, new()
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }
        return _client.From<T>();
    }

    /// <summary>
    /// Edge Function'ı çağırır
    /// </summary>
    /// <param name="functionName">Çağrılacak Edge Function'ın adı</param>
    /// <param name="funcoptions">Edge Function çağrısı için seçenekler</param>
    /// <returns>Edge Function'dan dönen sonuç</returns>
    public async Task<T> InvokeEdgeFunctionAsync<T>(string functionName, Supabase.Functions.Client.InvokeFunctionOptions? funcoptions = null)
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }

        try
        {
            var response = await _client.Functions.Invoke(functionName, options: funcoptions);
            _logger.LogInformation("{FunctionName} edge function başarıyla çağrıldı", functionName);
            return response != null ? JsonSerializer.Deserialize<T>(response, JsonOptions)! : default!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{FunctionName} edge function çağrılırken hata oluştu", functionName);
            throw;
        }
    }

    /// <summary>
    /// Supabase database function'ını çalıştırır
    /// </summary>
    /// <param name="functionName">Çalıştırılacak database function'ın adı</param>
    /// <param name="parameters">Function'a gönderilecek parametreler (opsiyonel)</param>
    /// <returns>Database function'dan dönen sonuç</returns>
    public async Task<T> ExecuteDatabaseFunctionAsync<T>(string functionName, object? parameters = null)
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }

        if (string.IsNullOrWhiteSpace(functionName))
        {
            throw new ArgumentException("Function adı boş olamaz.", nameof(functionName));
        }

        try
        {
            _logger.LogInformation("{FunctionName} database function çağrılıyor", functionName);

            // Supabase RPC (Remote Procedure Call) kullanarak database function'ı çağır
            var result = await _client.Rpc(functionName, parameters);

            // Sonucu belirtilen tipe dönüştür
            if (result == null)
            {
                _logger.LogWarning("{FunctionName} database function null sonuç döndü", functionName);
                return default!;
            }

            // JSON string ise deserialize et
            if (typeof(T) == typeof(string))
            {
                return (T)(object)result;
            }

            // Diğer tipler için JSON deserializasyon
            var jsonString = result.Content;
            if (string.IsNullOrEmpty(jsonString))
            {
                return default!;
            }

            var deserializedResult = JsonSerializer.Deserialize<T>(jsonString, JsonOptions);

            _logger.LogInformation("{FunctionName} database function başarıyla çağrıldı", functionName);
            return deserializedResult!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{FunctionName} database function çağrılırken hata oluştu. Parametreler: {@Parameters}", functionName, parameters);
            throw;
        }
    }

    /// <summary>
    /// Main tablosundan belirli bir anahtarın değerini alır
    /// </summary>
    /// <param name="key">Aranacak anahtar</param>
    /// <returns>Anahtar değeri</returns>
    public async Task<string?> GetMainValueAsync(string key)
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Supabase client henüz başlatılmadı. InitializeAsync() metodunu çağırın.");
        }

        if (string.IsNullOrWhiteSpace(key))
        {
            throw new ArgumentException("Anahtar boş olamaz.", nameof(key));
        }

        try
        {
            var response = await _client.From<MainData>()
                .Where(x => x.Name == key)
                .Single();

            return response?.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Main data değeri alınırken hata oluştu. Anahtar: {Key}", key);
            return null;
        }
    }
}