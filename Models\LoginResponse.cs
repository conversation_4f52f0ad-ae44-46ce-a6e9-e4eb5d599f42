namespace TranslationAgentServer.Models;

/// <summary>
/// Login yanıtı için kullanılan model sınıfı
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Login işleminin başarılı olup olmadığını belirtir
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Login işlemi ile ilgili mesaj
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Session ID (başarılı login durumunda)
    /// </summary>
    public string? SessionId { get; set; }
}