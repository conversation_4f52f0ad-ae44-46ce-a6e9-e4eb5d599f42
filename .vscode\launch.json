{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    
        {
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/bin/Debug/net9.0/TranslationAgentServer.dll",
            "args": [],
            "cwd": "${workspaceFolder}",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally"
            },
                   "console": "integratedTerminal",
            "pipeTransport": {
                "pipeProgram": "powershell",
                "pipeArgs": [
                    "-Command",
                    "chcp 65001 | Out-Null;"
                ],
                "debuggerPath": "${workspaceFolder}/.netcoredbg/netcoredbg",
                "debuggerArgs": [
                    "--interpreter=vscode"
                ],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}