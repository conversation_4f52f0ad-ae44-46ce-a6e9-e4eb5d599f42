using System;

namespace TranslationAgentServer.Models
{
    /// <summary>
    /// Metin analiz sonuçlarını temsil eden model
    /// </summary>
    public class TextAnalysisResult
    {
        /// <summary>
        /// İşlemin başarılı olup olmadığını belirtir.
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Hata mesajı (işlem başarısızsa)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Hata detayları (işlem başarısızsa)
        /// </summary>
        public string? ErrorDetails { get; set; }

        /// <summary>
        /// Orijinal metin
        /// </summary>
        public string? OriginalText { get; set; }

        /// <summary>
        /// Metnin karakter sayısı
        /// </summary>
        public int CharacterCount { get; set; }

        /// <summary>
        /// Metindeki keli<PERSON> sayısı
        /// </summary>
        public int WordCount { get; set; }

        /// <summary>
        /// Metindeki cümle sayısı
        /// </summary>
        public int SentenceCount { get; set; }

        /// <summary>
        /// Lemmatization işleminden geçmiş metin
        /// </summary>
        public string? LemmatizedText { get; set; }

        public TextAnalysisResult()
        {
            IsSuccess = false;
            CharacterCount = 0;
            WordCount = 0;
            SentenceCount = 0;
        }
    }
}