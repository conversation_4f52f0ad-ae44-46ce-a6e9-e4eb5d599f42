using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Gemini AI servisi arayüzü - içerik üretimi ve embedding işlemleri için
/// </summary>
public interface IGeminiService
{

    /// <summary>
    /// Gemini bağlantısını başlatır
    /// </summary>
    /// <returns>Asenkron işlem</returns>
    bool InitializeAsync(string apiKey);


    /// <summary>
    /// Belirtilen model ve ayarlarla metin içeriği üretir
    /// </summary>
    /// <param name="request">İçerik üretim isteği</param>
    /// <returns>Üretilen içerik yanıtı</returns>
    Task<GeminiContentResponse> GenerateContentAsync(GeminiContentRequest request);

    /// <summary>
    /// Tekli metin için embedding üretir
    /// </summary>
    /// <param name="text">Embedding üretilecek metin</param>
    /// <param name="model">Kullanılacak embedding modeli (varsayılan: text-embedding-004)</param>
    /// <returns>Embedding vektörü</returns>
    Task<GeminiEmbeddingResponse> GenerateEmbeddingAsync(string text, string? model = null);

    /// <summary>
    /// Çoklu metin için embedding üretir
    /// </summary>
    /// <param name="texts">Embedding üretilecek metinler listesi</param>
    /// <param name="model">Kullanılacak embedding modeli (varsayılan: text-embedding-004)</param>
    /// <returns>Embedding vektörleri listesi</returns>
    Task<GeminiMultipleEmbeddingResponse> GenerateMultipleEmbeddingsAsync(List<string> texts, string? model = null);


    /// <summary>
    /// Metinler için embedding üretir
    /// </summary>
    /// <param name="texts">Embedding üretilecek metinler listesi</param>
    /// <returns>Embedding vektörleri listesi</returns>
    Task<List<List<float>>> GetEmbeddingsAsync(List<string> texts);

}