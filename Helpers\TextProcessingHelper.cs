using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Catalyst;
using Mosaik.Core;
using TranslationAgentServer.Interfaces;


namespace TranslationAgentServer.Helpers
{
    /// <summary>
    /// Metin işleme için yardımcı fonksiyonlar
    /// </summary>
    public static class TextProcessingHelper
    {
        private static INlpService? _nlpService;

        /// <summary>
        /// NLP servisini ayarlar (Dependency Injection için)
        /// </summary>
        /// <param name="nlpService">NLP servisi</param>
        public static void SetNlpService(INlpService nlpService)
        {
            _nlpService = nlpService;
        }

        /// <summary>
        /// Verilen terimi temizler ve stopword'leri kaldırır
        /// </summary>
        /// <param name="term">Temizlenecek terim</param>
        /// <returns>Temizlenmiş terim</returns>
        /// 
        public static string TermCleanStopWords(string? term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return string.Empty;
            // Stopword'leri temizle (a, an, the)

            string[] stopwords = { "a", "an", "the" };
            foreach (var stopword in stopwords)
            {
                // Kelimenin başında ve ardından bir boşlukla eşleşen stopword'leri temizle
                term = Regex.Replace(term, $"^{stopword}\\s+", "", RegexOptions.IgnoreCase);
            }

            return term;
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir ve temizler (Pipeline ile)
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <param name="nlp">Catalyst Pipeline nesnesi</param>
        /// <returns>Lemmatized ve temizlenmiş metin</returns>
        /// 
        public static string ProcessTextToLemma(string? text, Pipeline nlp)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            try
            {

                // Metni küçük harfe çevir ve İngilizce locale kullan
                var sentenceDoc = new Document(text.ToLower(CultureInfo.GetCultureInfo("en-GB")), Language.English);

                // NLP işlemini uygula
                nlp.ProcessSingle(sentenceDoc);

                // Lemma'ları çıkar ve noktalama işaretlerini filtrele
                var lemmatizedText = string.Join(" ", sentenceDoc.SelectMany(s => s.Select(t => t.Lemma)))
                    .Where(c => !char.IsPunctuation(c) && !char.IsSymbol(c))
                    .Aggregate(new StringBuilder(), (sb, c) => sb.Append(c))
                    .ToString();

                // Çoklu boşlukları tek boşluğa dönüştür ve trim et
                return Regex.Replace(lemmatizedText, @"\s+", " ").Trim();
            }
            catch (Exception)
            {
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir (NLP Service ile - Async)
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <returns>Lemmatized ve temizlenmiş metin</returns>
        public static async Task<string> ProcessTextToLemmaAsync(string? text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (_nlpService == null)
            {
                // NLP servisi yoksa basit temizleme yap
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }

            try
            {
                return await _nlpService.ProcessTextToLemmaAsync(text);
            }
            catch (Exception)
            {
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir (NLP Service ile - Sync)
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <returns>Lemmatized ve temizlenmiş metin</returns>
        public static string ProcessTextToLemmaSync(string? text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (_nlpService == null)
            {
                // NLP servisi yoksa basit temizleme yap
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }

            try
            {
                // Async metodu sync olarak çağır (dikkatli kullanım gerekir)
                return _nlpService.ProcessTextToLemmaAsync(text).GetAwaiter().GetResult();
            }
            catch (Exception)
            {
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }

        /// <summary>
        /// Verilen metni belirtilen uzunlukta kısaltır ve sonuna "..." ekler.
        /// </summary>
        /// <param name="text">Kısaltılacak metin.</param>
        /// <param name="maxLength">Maksimum uzunluk.</param>
        /// <returns>Kısaltılmış metin.</returns>
        public static string TruncateText(string? text, int maxLength)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }
    }
}
