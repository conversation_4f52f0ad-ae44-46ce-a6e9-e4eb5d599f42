{"type":"entity","name":"TranslationAgentServer","entityType":"project","observations":["Supabase 'projects' tablosu için CRUD ve arama işlevleri eklendi.","`Project` modeli, `IProjectService`, `ProjectService` ve `ProjectController` sınıfları oluşturuldu.",".NET 9.0 Web API projesi","Supabase veritabanı entegrasyonu","Render.com deployment desteği","Session tabanlı kimlik doğrulama sistemi","Google Sheets API entegrasyonu","Modüler klasör yapısı (Controllers, Services, Interfaces, Models, Configurations)","Docker containerization desteği","Health check endpoint'leri","RESTful API tasarımı","Catalyst NLP kütüphanesi entegrasyonu tamamlandı","INlpService arayüzü oluşturuldu","NlpService implementasyonu eklendi","NlpController API endpoint'leri eklendi","NlpModels model sınıfları oluşturuldu","TextProcessingHelper'a NLP servisi entegrasyonu yapıldı","Program.cs'de DI container'a NLP servisi eklendi","NLP servisi arka planda başlatılacak şekilde yapılandırıldı","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize","Gemini AI servisi başarıyla eklendi","mscraftsman/generative-ai kütüphanesi projeye entegre edildi","GeminiService sadece servis olarak eklendi, controller eklenmedi","Supabase'den gemini_api anahtarı alınarak servis başlatılıyor","Generate content fonksiyonu model ve ince ayar seçimi ile eklendi","Generate embedding fonksiyonu tekli ve çoklu metin için ayrı olarak eklendi","Dönen yapı gelen mesajı döndürüyor","Proje başarıyla build edildi","Context modeli, servisi ve kontrolcüsü başarıyla eklendi","Program.cs dosyasına ContextService DI kaydı eklendi","Context API endpoint'leri Program.cs'e eklendi","Tüm derleme hataları çözüldü ve proje başarıyla derlendi","ContextService Supabase API kullanımı düzeltildi","Yeni CreateTerimceSheet endpoint'i başarıyla GoogleSheetsController.cs dosyasına eklendi","Endpoint Supabase'den terms_columns verisini alıp parse ederek Terimce sayfası oluşturuyor","Program.cs dosyasında ana sayfa endpoint'ine createTerimceSheet eklendi","Proje başarıyla derlendi, sadece Information/Warning seviyesinde sorunlar var","CreateTerimceSheet endpoint'ine sayfa varlık kontrolü eklendi","Endpoint artık aynı isimde sayfa varsa Conflict (409) döndürüyor","Proje başarıyla derlendi, hiç ERROR seviyesinde sorun yok","Context yönetimi sistemi eklendi","Gemini AI entegrasyonu tamamlandı","NLP servisi Catalyst ile entegre edildi","Embedding tabanlı arama özellikleri","Full-text search implementasyonu","Şema tabanlı çoklu proje desteği","Helper sınıfları ile yardımcı fonksiyonlar","Modüler ve genişletilebilir mimari","Retry mekanizması başarıyla eklendi","RetryHelper sınıfı ile HTTP istekleri için otomatik retry desteği","GeminiService.GetEmbeddingsAsync metoduna retry mekanizması entegre edildi","TextService.CreateTextsAsync metodundaki batch embedding işlemine retry desteği eklendi","Too Many Requests (429) hatalarına karşı exponential backoff ile retry","Maksimum 3 deneme, 2 saniye temel bekleme süresi","Rate limiting için batch'ler arası bekleme süresi 3.5 saniyeden 1.5 saniyeye düşürüldü","Proje başarıyla derlendi, ERROR seviyesinde hiç sorun yok"]}
{"type":"entity","name":"Models","entityType":"folder","observations":["Veri modellerini barındıran klasör","Project - proje veri modeli","Process - işlem veri modeli","LoginRequest, LoginResponse - kimlik doğrulama modelleri","MainData - sistem ana veri modeli","GoogleSheetsModels - Google Sheets veri modelleri","Supabase.Postgrest.Models.BaseModel'den türetilmiş","Context - context veri modeli ve DTO'ları","GeminiModels - Gemini AI veri modelleri","TextAnalysisResult - NLP analiz sonuçları","ContextStatistics - context istatistikleri","Embedding desteği olan modeller"]}
{"type":"entity","name":"Configurations","entityType":"folder","observations":["Uygulama konfigürasyon sınıfları","SupabaseOptions - Supabase bağlantı ayarları","Options pattern implementasyonu","appsettings.json ile bağlantılı","Environment-specific ayarlar","Environment-specific konfigürasyonlar"]}
{"type":"entity","name":"CreateSchemaResult","entityType":"Model","observations":["Supabase'de şema oluşturma fonksiyonunun sonucunu temsil eder","Schema_Created ve Tables_Created alanları eklendi","Yeni JSON formatına uygun olarak güncellendi: {\"message\":\"...\",\"success\":true,\"schema_created\":true,\"tables_created\":[...]}","Created alanı Schema_Created olarak yeniden adlandırıldı","Tables_Created alanı List<string> tipinde eklendi","Success, Message, Schema_Created ve Tables_Created özelliklerini içerir"]}
{"type":"entity","name":"Catalyst","entityType":"NLP Library","observations":["curiosity-ai/catalyst kütüphanesi context7 ile araştırıldı","Catalyst.Models.English paketleri projede zaten mevcuttu","NLP pipeline'ı için English modeli kullanılıyor","Lemmatization, text analysis ve multiple text processing özellikleri eklendi","Pipeline statik fonksiyonlarda kullanılabilir hale getirildi"]}
{"type":"entity","name":"Text","entityType":"Model","observations":["Represents the text data model.","Corresponds to a table in the Supabase database."]}
{"type":"entity","name":"ITextService","entityType":"Interface","observations":["Defines the contract for text management services.","Includes methods for CRUD operations and other text-related functionalities.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren metot imzalarına eklendi","GetAllTextsAsync, GetTextsBySheetIdAsync, GetTextsByNamespaceAsync, GetTextsByStatusAsync, SearchTextsAsync metotlarına sayfalama parametreleri eklendi","Varsayılan değerler: page = 1, pageSize = 50","XML dokümantasyon yorumları güncellendi"]}
{"type":"entity","name":"TextService","entityType":"Service","observations":["Implements the `ITextService` interface.","Handles the business logic for text operations.","Interacts with the Supabase database via `ISupabaseService`.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren metotlara eklendi","GetAllTextsAsync, GetTextsBySheetIdAsync, GetTextsByNamespaceAsync, GetTextsByStatusAsync, SearchTextsAsync metotlarına sayfalama desteği eklendi","Supabase Range() metodu kullanılarak offset ve limit hesaplaması yapılıyor","Varsayılan sayfa boyutu 50, sayfa numarası 1'den başlar","Log mesajlarına sayfa ve boyut bilgileri eklendi","GetAllTextsAsync metoduna Namespace, Key, En, Tr ve Status alanlarına göre filtreleme özelliği eklendi","Filtreleme, veritabanından veri alındıktan sonra LINQ ile uygulanıyor","Status filtresi enum değerine göre, diğer filtreler Contains metodu ile çalışıyor","Proje başarıyla build edildi ve filtreleme özelliği çalışır durumda","CreateTextsAsync metodundaki batch embedding işlemine retry eklendi","Her batch için ayrı retry mekanizması","Batch'ler arası bekleme süresi 1.5 saniyeye optimize edildi","Rate limiting ve retry ile daha güvenilir embedding üretimi"]}
{"type":"entity","name":"TextController","entityType":"Controller","observations":["Provides RESTful API endpoints for text management.","Uses the `ITextService` to perform text operations.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren endpoint'lere eklendi","GetAllTexts, GetTextsBySheetId, GetTextsByNamespace, GetTextsByStatus, SearchTexts metotlarına sayfalama desteği eklendi","Sayfa numarası 1'den başlar, varsayılan sayfa boyutu 50","Maksimum sayfa boyutu 1000 ile sınırlandırıldı","Query parametreleri olarak [FromQuery] ile alınıyor"]}
{"type":"entity","name":"TermController","entityType":"Controller","observations":["TermController başarıyla TextController'ın endpoint yapısına benzer şekilde düzenlendi","Tüm endpoint'ler {schemaName} parametresi ile başlayacak şekilde güncellendi","ITermService arayüzündeki metotlara uygun olarak schemaName parametreleri eklendi","Endpoint'ler: GET {schemaName}, GET {schemaName}/{id}, GET {schemaName}/sheet/{sheetId}, GET {schemaName}/status/{status}, GET {schemaName}/search, GET {schemaName}/fulltext-search, GET {schemaName}/find/english/{englishTerm}, GET {schemaName}/find/turkish/{turkishTerm}, GET {schemaName}/find/lemma/{lemma}, GET {schemaName}/untranslated, GET {schemaName}/without-embedding, GET {schemaName}/statistics, POST {schemaName}/similar, POST {schemaName}, POST {schemaName}/bulk, PUT {schemaName}/{id}, DELETE {schemaName}/{id}, DELETE {schemaName}/bulk, DELETE {schemaName}/sheet/{sheetId}, PUT {schemaName}/{id}/embedding, PUT {schemaName}/embeddings/bulk","Proje başarıyla build oldu, hiç hata yok","Program.cs dosyasındaki terms endpoint'leri TermController'ın yeni schemaName parametreli yapısına uygun olarak güncellendi","Tüm endpoint'ler {schemaName} parametresi ile başlayacak şekilde düzenlendi"]}
{"type":"entity","entityType":"Service","name":"ContextService","observations":["ContextService.cs dosyasındaki Supabase API kullanım hataları düzeltildi","Constants.Ordering.Descending kullanımları Supabase.Postgrest.Constants.Ordering.Descending olarak güncellendi","Or() metodu Where() ile Contains() kullanımına çevrildi","TextSearch() metodu Where() ile Contains() kullanımına çevrildi","Not() metodu Where() ile null kontrolüne çevrildi","Proje başarıyla derlendi ve hiç hata kalmadı"]}
{"type":"entity","name":"ContextController","entityType":"Controller","observations":["ContextController'daki tüm endpoint'lerde schemaName parametresi query parametresinden route parametresine başarıyla dönüştürüldü","Tüm HTTP metodları (GET, POST, PUT, DELETE) için route yapısı {schemaName} ile başlayacak şekilde güncellendi","Program.cs dosyasındaki context endpoint URL'leri yeni route yapısına uygun olarak güncellendi","CreateContext metodundaki CreatedAtAction çağrısı parametre sırası düzeltilerek güncellendi","Proje başarıyla derlendi ve ERROR seviyesinde problem bulunmuyor"]}
{"type":"entity","name":"ContextController.cs","entityType":"Dosya","observations":["`GetAllContexts` endpoint'ine `categoryFilter`, `titleFilter` ve `contentFilter` parametreleri eklenerek filtreleme özelliği uygulandı."]}
{"type":"entity","name":"ContextService.cs","entityType":"Dosya","observations":["`_supabaseClient` kullanımları `ISupabaseService` ile değiştirildi.","`GetAllContextsAsync`, `GetContextByIdAsync`, `SearchContextsAsync`, `FullTextSearchContextsAsync`, `FindSimilarContextsAsync`, `CreateContextAsync`, `UpdateContextAsync`, `DeleteContextAsync`, `CreateContextsAsync`, `DeleteContextsAsync` ve `GetContextStatisticsAsync` metotları güncellendi."]}
{"type":"entity","name":"TermService.cs","entityType":"Dosya","observations":["Birincil oluşturucu kullanımı, gereksiz `using` yönergesi, koleksiyon başlatma kolaylaştırması ve desen eşleştirme kullanımı ile ilgili bilgi düzeyindeki sorunlar tespit edildi."]}
{"type":"entity","name":"TermController.cs","entityType":"Dosya","observations":["Birincil oluşturucu kullanımı ve `Count` değerinin `Any()` yerine 0 ile karşılaştırılması gerektiği ile ilgili bilgi düzeyindeki sorunlar tespit edildi."]}
{"type":"entity","name":"TranslationAgentServer Projesi","entityType":"Proje","observations":["`ContextController.cs` dosyasındaki `GetAllContexts` endpoint'ine filtreleme parametreleri eklendi.","`ContextService.cs` dosyasındaki `_supabaseClient` kullanımları `ISupabaseService` ile değiştirildi.","Tüm tespit edilen hatalar giderildi ve proje tutarlı hale getirildi."]}
{"type":"entity","name":"GoogleSheetsController","entityType":"controller","observations":["ValidateTextsColumns endpoint eklendi - Google Sheets'teki texts sayfasının sütun yapısını Supabase'deki texts_columns verisiyle karşılaştırır","ValidateTermsColumns endpoint eklendi - Google Sheets'teki terms sayfasının sütun yapısını Supabase'deki terms_columns verisiyle karşılaştırır","Her iki endpoint de spreadsheetId ve sheetId parametrelerini alır ve doğrulama sonucunu döner"]}
{"type":"entity","name":"ProcessBackgroundService","entityType":"Service","observations":["IHostedService arayüzünü uygulayan arka plan servisi oluşturuldu","ProcessTaskInfo sınıfı ile süreç bilgilerini yönetir","Süreçleri başlatma, durdurma, iptal etme, duraklatma, devam ettirme işlevleri","Durum ve ilerleme takibi yapabilir","Program.cs'e DI container'a eklendi","ProcessService ile entegre çalışacak şekilde tasarlandı"]}
{"type":"entity","name":"IProcessBackgroundService","entityType":"Interface","observations":["ProcessBackgroundService için arayüz oluşturuldu","StartTaskAsync, CancelTaskAsync, PauseTaskAsync, ResumeTaskAsync metotları","GetTaskStatusAsync, GetTaskProgressAsync, GetActiveTasksAsync metotları","ProcessTaskStatus enum'u tanımlandı"]}
{"type":"entity","name":"IContextService","entityType":"Interface","observations":["Context servisi için arayüz tanımlar","CRUD işlemleri için metot imzaları","Arama ve filtreleme metotları","Embedding tabanlı benzerlik arama","Toplu işlemler için metotlar","Context istatistikleri metodu","Şema tabanlı operasyonlar"]}
{"type":"entity","name":"Context","entityType":"Model","observations":["Supabase contexts tablosunu temsil eder","Category, Title, Content alanları","CombinedText ve CombinedTsvector otomatik alanları","Embedding vektörü desteği","CreatedAt ve UpdatedAt timestamp alanları","ContextCreateDto ve ContextUpdateDto DTO sınıfları","ContextStatistics model sınıfı","Supabase.Postgrest.Models.BaseModel'den türetilmiş"]}
{"type":"entity","name":"GeminiService","entityType":"Service","observations":["IGeminiService arayüzünü uygular","Google Gemini AI modelleri ile entegrasyon","İçerik üretimi (GenerateContentAsync)","Tekli ve çoklu embedding üretimi","Model bilgisi alma özellikleri","mscraftsman/generative-ai kütüphanesi kullanır","Supabase'den API anahtarı alarak başlatılır","Sadece servis olarak eklendi, controller yok","GetEmbeddingsAsync metoduna retry mekanizması eklendi","GetEmbeddingsInternalAsync private metodu ile asıl işlem ayrıştırıldı","RetryHelper.ExecuteWithRetryAsync kullanarak otomatik retry","3 deneme, 2 saniye temel bekleme süresi","Too Many Requests hatalarına karşı korumalı"]}
{"type":"entity","name":"IGeminiService","entityType":"Interface","observations":["Gemini AI servisi için arayüz tanımlar","İçerik üretimi metotları","Embedding üretimi metotları","Model bilgisi alma metotları","Servis başlatma metodu"]}
{"type":"entity","name":"GeminiModels","entityType":"Model","observations":["Gemini AI servisi için veri modelleri","GeminiContentRequest - içerik üretim isteği","GeminiContentResponse - içerik üretim yanıtı","GeminiEmbeddingResponse - tekli embedding yanıtı","GeminiMultipleEmbeddingResponse - çoklu embedding yanıtı","GeminiModel - model bilgisi","Temperature, TopP, TopK parametreleri","JSON çıktı formatı desteği","Google Search grounding desteği"]}
{"type":"entity","name":"NlpService","entityType":"Service","observations":["INlpService arayüzünü uygular","Catalyst NLP kütüphanesi kullanır","Lemmatization işlemleri","Detaylı metin analizi","Pipeline başlatma ve hazırlık","TextAnalysisResult döndürür","Arka planda başlatılacak şekilde yapılandırılmış"]}
{"type":"entity","name":"INlpService","entityType":"Interface","observations":["NLP servisi için arayüz tanımlar","Pipeline başlatma metodu","Lemmatization metodu","Metin analizi metodu","Pipeline durumu kontrolü"]}
{"type":"entity","name":"NlpController","entityType":"Controller","observations":["NLP işlemleri için RESTful API endpoint'leri","Status kontrolü endpoint'i","Lemmatization endpoint'i","Metin analizi endpoint'i","Çoklu metin işleme endpoint'i","Pipeline başlatma endpoint'i","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize"]}
{"type":"entity","name":"TextAnalysisResult","entityType":"Model","observations":["Metin analiz sonuçlarını temsil eder","IsSuccess, ErrorMessage, ErrorDetails alanları","OriginalText, CharacterCount, WordCount, SentenceCount","LemmatizedText alanı","NLP servisi tarafından döndürülür"]}
{"type":"entity","name":"EmbeddingHelper","entityType":"Helper","observations":["Embedding oluşturma işlemleri için yardımcı sınıf","Supabase edge function kullanır","Tekli ve çoklu embedding üretimi","small-embedding fonksiyonunu çağırır","Cosine benzerlik hesaplama","ISupabaseService bağımlılığı"]}
{"type":"entity","name":"TextProcessingHelper","entityType":"Helper","observations":["Metin işleme için yardımcı fonksiyonlar","Stopword temizleme","Lemmatization işlemleri","NLP servisi entegrasyonu","Async ve sync metotlar","Metin kısaltma fonksiyonu","Catalyst Pipeline desteği"]}
{"type":"entity","name":"SupabaseOptions","entityType":"Configuration","observations":["Supabase bağlantı ayarlarını içerir","Url ve Key özellikleri","Options pattern implementasyonu","appsettings.json ile bağlantılı"]}
{"type":"entity","name":"Helpers","entityType":"folder","observations":["Yardımcı fonksiyonları barındıran klasör","EmbeddingHelper - embedding oluşturma işlemleri","TextProcessingHelper - metin işleme fonksiyonları","Supabase edge function entegrasyonu","NLP servisi entegrasyonu","Cosine benzerlik hesaplama","Stopword temizleme ve lemmatization"]}
{"type":"entity","name":"Controllers Folder","entityType":"Folder","observations":["API endpoint'lerini barındıran klasör","8 adet controller dosyası içerir","AuthController - kimlik doğrulama endpoint'leri","ContextController - context CRUD işlemleri","GoogleSheetsController - Google Sheets API işlemleri","HealthController - sistem durumu kontrolü","ProcessController - background işlem yönetimi","ProjectController - proje CRUD işlemleri","TermController - terim yönetimi","TextController - metin yönetimi","RESTful API tasarım prensiplerine uygun"]}
{"type":"entity","name":"Services Folder","entityType":"Folder","observations":["İş mantığını barındıran servis sınıfları","10 adet servis dosyası içerir","AuthService - kimlik doğrulama işlemleri","ContextService - context CRUD işlemleri","GeminiService - AI içerik üretimi ve embedding","GoogleSheetsService - Google Sheets API işlemleri","NlpService - doğal dil işleme operasyonları","ProcessService - background işlem yönetimi","ProjectService - proje CRUD işlemleri","SupabaseService - veritabanı bağlantı yönetimi","TermService - terim yönetimi","TextService - metin yönetimi","Dependency injection pattern kullanımı"]}
{"type":"entity","name":"Interfaces Folder","entityType":"Folder","observations":["Servis arayüzlerini barındıran klasör","10 adet interface dosyası içerir","IAuthService, IContextService, IGeminiService","IGoogleSheetsService, INlpService, IProcessService","IProjectService, ISupabaseService, ITermService, ITextService","Loose coupling ve testability sağlar","Contract-based programming yaklaşımı"]}
{"type":"entity","name":"Models Folder","entityType":"Folder","observations":["Veri modellerini barındıran klasör","12 adet model dosyası içerir","Context.cs - context veri modeli","CreateSchemaResult.cs - şema oluşturma sonucu","GeminiModels.cs - Gemini AI modelleri","GoogleSheetsModels.cs - Google Sheets veri modelleri","LoginRequest.cs, LoginResponse.cs - kimlik doğrulama modelleri","MainData.cs - ana sistem verileri","Process.cs - background işlem modeli","Project.cs - proje veri modeli","Term.cs - terim veri modeli","Text.cs - metin veri modeli","TextAnalysisResult.cs - metin analiz sonuçları","Supabase.Postgrest.Models.BaseModel'den türetilmiş modeller"]}
{"type":"entity","name":"Helpers Folder","entityType":"Folder","observations":["Yardımcı sınıfları barındıran klasör","2 adet helper dosyası içerir","EmbeddingHelper.cs - embedding işlemleri","TextProcessingHelper.cs - metin işleme operasyonları","NLP servisi entegrasyonu","Statik yardımcı metodlar"]}
{"type":"entity","name":"Configurations Folder","entityType":"Folder","observations":["Yapılandırma sınıflarını barındıran klasör","SupabaseOptions.cs - Supabase bağlantı ayarları","Options pattern implementasyonu","Dependency injection ile yapılandırma"]}
{"type":"entity","name":"Middleware Folder","entityType":"Folder","observations":["Özel middleware'leri barındıran klasör","AuthenticationMiddleware.cs - kimlik doğrulama middleware'i","Session validation","Public endpoint'ler tanımlaması","401 Unauthorized response yönetimi"]}
{"type":"entity","name":"Authentication System","entityType":"Feature","observations":["Session tabanlı kimlik doğrulama","Tek şifre ile giriş sistemi","Supabase main tablosundaki password ile doğrulama","30 dakika session süresi","Cookie tabanlı session yönetimi","HttpOnly, Secure, SameSite cookie ayarları","Thread-safe session yönetimi","ConcurrentDictionary ile aktif session'lar","Otomatik expired session temizleme","Public endpoint'ler: /health, /api/auth/*, /swagger"]}
{"type":"entity","name":"Google Sheets Integration","entityType":"Feature","observations":["Google Sheets API v4 entegrasyonu","Service account JSON kimlik doğrulama","Spreadsheet ve worksheet yönetimi","Veri okuma ve yazma işlemleri","Batch operations desteği","Satır ekleme, silme ve güncelleme","Header validation işlevselliği","'#|Namespace|Key|EN|TR|EN-TR' formatı desteği","HeaderValidationResult modeli","Production-ready durum"]}
{"type":"entity","name":"Gemini AI Integration","entityType":"Feature","observations":["mscraftsman/generative-ai kütüphanesi entegrasyonu","Supabase'den gemini_api anahtarı alımı","Generate content fonksiyonu","Model ve ince ayar seçimi","Generate embedding fonksiyonu","Tekli ve çoklu metin embedding","AI destekli içerik üretimi"]}
{"type":"entity","name":"NLP Integration","entityType":"Feature","observations":["Catalyst NLP kütüphanesi entegrasyonu","Doğal dil işleme operasyonları","Lemmatization işlemleri","Text analysis özellikleri","Multiple text processing","Background service olarak çalışma","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize"]}
{"type":"entity","name":"Background Process System","entityType":"Feature","observations":["Background task yönetimi","Process durumu takibi (Running, Paused, Completed, Failed)","Task type desteği (Translation, Analysis, Export, Import)","Real-time işlem durumu","Resume, pause, cancel işlemleri","Active task listesi","Progress tracking","Background service entegrasyonu"]}
{"type":"entity","name":"Database Schema","entityType":"Feature","observations":["Supabase PostgreSQL veritabanı","projects tablosu - proje bilgileri","main tablosu - sistem ayarları ve şifre","processes tablosu - background işlem durumları","contexts tablosu - context verileri","terms tablosu - terim verileri","texts tablosu - metin verileri","Embedding vektör desteği","Full-text search özellikleri","Row Level Security (RLS) politikaları","Real-time özellikler","REST API desteği"]}
{"type":"entity","name":"Deployment Configuration","entityType":"Feature","observations":["Docker multi-stage build","mcr.microsoft.com/dotnet/sdk:9.0 build image","mcr.microsoft.com/dotnet/aspnet:9.0 runtime image","Port 8080 expose","Render.com deployment yapılandırması","render.yaml konfigürasyonu","Auto-deploy aktif","Health check path: /health","Free plan konfigürasyonu","Environment variables yönetimi"]}
{"type":"entity","name":"ProcessService","entityType":"Service","observations":["ExecuteTextTranslationTask fonksiyonu Google Sheets ve Text servisleri ile entegre edildi","Google Sheets'ten veri çekme ve toplu metin oluşturma işlevselliği eklendi","IProjectService ve ITextService bağımlılıkları eklendi","GoogleSheetsApiResponse.Success property'si kullanılarak hata düzeltildi","Process task'larını yöneten ana servis sınıfı"]}
{"type":"entity","name":"RetryHelper","entityType":"Helper Class","observations":["HTTP istekleri için retry mekanizması sağlayan static helper sınıf","ExecuteWithRetryAsync generic metodu ile herhangi bir async operasyonu retry edebilir","429, 500, 502, 503, 504 HTTP hata kodları için retry desteği","Exponential backoff algoritması ile bekleme süreleri","Jitter eklenerek thundering herd problemini önler","Maksimum 30 saniye bekleme süresi sınırı","Detaylı loglama ile retry süreçlerini takip eder"]}
{"type":"relation","from":"TranslationAgentServer","to":"Models","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Configurations","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"CreateSchemaResult","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Catalyst","relationType":"uses"}
{"type":"relation","from":"TextController","to":"ITextService","relationType":"uses"}
{"type":"relation","from":"TextService","to":"ITextService","relationType":"implements"}
{"type":"relation","from":"TextService","to":"Text","relationType":"uses"}
{"type":"relation","from":"ProcessBackgroundService","to":"IProcessBackgroundService","relationType":"implements"}
{"type":"relation","from":"ContextController","to":"IContextService","relationType":"depends on"}
{"type":"relation","from":"ContextService","to":"IContextService","relationType":"implements"}
{"type":"relation","from":"GeminiService","to":"IGeminiService","relationType":"implements"}
{"type":"relation","from":"NlpService","to":"INlpService","relationType":"implements"}
{"type":"relation","from":"NlpController","to":"INlpService","relationType":"depends on"}
{"type":"relation","from":"TextProcessingHelper","to":"INlpService","relationType":"integrates with"}
{"type":"relation","from":"GeminiModels","to":"GeminiService","relationType":"used by"}
{"type":"relation","from":"TextAnalysisResult","to":"NlpService","relationType":"returned by"}
{"type":"relation","from":"Helpers","to":"EmbeddingHelper","relationType":"contains"}
{"type":"relation","from":"Helpers","to":"TextProcessingHelper","relationType":"contains"}
{"type":"relation","from":"Models","to":"Context","relationType":"contains"}
{"type":"relation","from":"Models","to":"GeminiModels","relationType":"contains"}
{"type":"relation","from":"Models","to":"TextAnalysisResult","relationType":"contains"}
{"type":"relation","from":"Configurations","to":"SupabaseOptions","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Controllers Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Services Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Interfaces Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Models Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Helpers Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Configurations Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Middleware Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Authentication System","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Google Sheets Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Gemini AI Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"NLP Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Background Process System","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Database Schema","relationType":"uses"}
{"type":"relation","from":"TranslationAgentServer","to":"Deployment Configuration","relationType":"configured_with"}
{"type":"relation","from":"Controllers Folder","to":"Services Folder","relationType":"depends_on"}
{"type":"relation","from":"Services Folder","to":"Interfaces Folder","relationType":"implements"}
{"type":"relation","from":"Services Folder","to":"Models Folder","relationType":"uses"}
{"type":"relation","from":"Services Folder","to":"Helpers Folder","relationType":"uses"}
{"type":"relation","from":"Services Folder","to":"Database Schema","relationType":"interacts_with"}
{"type":"relation","from":"Authentication System","to":"Middleware Folder","relationType":"implemented_in"}
{"type":"relation","from":"Google Sheets Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"Gemini AI Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"NLP Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"Background Process System","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"TranslationAgentServer","to":"RetryHelper","relationType":"uses"}
{"type":"relation","from":"GeminiService","to":"RetryHelper","relationType":"integrates"}
{"type":"relation","from":"TextService","to":"RetryHelper","relationType":"integrates"}
{"type":"relation","from":"GeminiService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"TextService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"RetryHelper","to":"TranslationAgentServer","relationType":"belongs_to"}