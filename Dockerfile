# Render.com için .NET Core Web API Dockerfile
# Multi-stage build kull<PERSON>rak optimize edilmiş Docker image oluşturur

# Build stage - SDK image kullanarak uygulamayı derle
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Proje dosyasını kopyala ve bağımlılıkları restore et
COPY *.csproj ./
RUN dotnet restore

# Kaynak kodları kopyala ve uygulamayı derle
COPY . ./
RUN dotnet publish -c Release -o /app/publish

# Runtime stage - Sadece runtime image kullanarak final image oluştur
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app

# Build stage'den derlenmiş uygulamayı kopyala
COPY --from=build /app/publish .

# Render.com için gerekli port konfigürasyonu
# Render.com PORT environment variable'ını kullanır
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Uygulamayı başlat
ENTRYPOINT ["dotnet", "TranslationAgentServer.dll"]